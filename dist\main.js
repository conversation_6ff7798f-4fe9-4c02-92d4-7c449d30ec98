(()=>{var pe=Object.create;var Xt=Object.defineProperty;var de=Object.getOwnPropertyDescriptor;var xe=Object.getOwnPropertyNames;var ue=Object.getPrototypeOf,be=Object.prototype.hasOwnProperty;var ge=(e,i)=>()=>(i||e((i={exports:{}}).exports,i),i.exports);var ye=(e,i,o,r)=>{if(i&&typeof i=="object"||typeof i=="function")for(let s of xe(i))!be.call(e,s)&&s!==o&&Xt(e,s,{get:()=>i[s],enumerable:!(r=de(i,s))||r.enumerable});return e};var wt=(e,i,o)=>(o=e!=null?pe(ue(e)):{},ye(i||!e||!e.__esModule?Xt(o,"default",{value:e,enumerable:!0}):o,e));var xt=ge((Ft,dt)=>{(function(e,i){typeof define=="function"&&define.amd?define([],function(){return i(e)}):typeof dt=="object"&&typeof dt.exports=="object"?dt.exports=i(e):e.Notiflix=i(e)})(typeof global>"u"?typeof window>"u"?Ft:window:global,function(e){"use strict";if(typeof e>"u"&&typeof e.document>"u")return!1;var i,o,r,s,c,x=`

Visit documentation page to learn more: https://notiflix.github.io/documentation`,b='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',N={Success:"Success",Failure:"Failure",Warning:"Warning",Info:"Info"},d={wrapID:"NotiflixNotifyWrap",overlayID:"NotiflixNotifyOverlay",width:"280px",position:"right-top",distance:"10px",opacity:1,borderRadius:"5px",rtl:!1,timeout:3e3,messageMaxLength:110,backOverlay:!1,backOverlayColor:"rgba(0,0,0,0.5)",plainText:!0,showOnlyTheLastOne:!1,clickToClose:!1,pauseOnHover:!0,ID:"NotiflixNotify",className:"notiflix-notify",zindex:4001,fontFamily:"Quicksand",fontSize:"13px",cssAnimation:!0,cssAnimationDuration:400,cssAnimationStyle:"fade",closeButton:!1,useIcon:!0,useFontAwesome:!1,fontAwesomeIconStyle:"basic",fontAwesomeIconSize:"34px",success:{background:"#32c682",textColor:"#fff",childClassName:"notiflix-notify-success",notiflixIconColor:"rgba(0,0,0,0.2)",fontAwesomeClassName:"fas fa-check-circle",fontAwesomeIconColor:"rgba(0,0,0,0.2)",backOverlayColor:"rgba(50,198,130,0.2)"},failure:{background:"#ff5549",textColor:"#fff",childClassName:"notiflix-notify-failure",notiflixIconColor:"rgba(0,0,0,0.2)",fontAwesomeClassName:"fas fa-times-circle",fontAwesomeIconColor:"rgba(0,0,0,0.2)",backOverlayColor:"rgba(255,85,73,0.2)"},warning:{background:"#eebf31",textColor:"#fff",childClassName:"notiflix-notify-warning",notiflixIconColor:"rgba(0,0,0,0.2)",fontAwesomeClassName:"fas fa-exclamation-circle",fontAwesomeIconColor:"rgba(0,0,0,0.2)",backOverlayColor:"rgba(238,191,49,0.2)"},info:{background:"#26c0d3",textColor:"#fff",childClassName:"notiflix-notify-info",notiflixIconColor:"rgba(0,0,0,0.2)",fontAwesomeClassName:"fas fa-info-circle",fontAwesomeIconColor:"rgba(0,0,0,0.2)",backOverlayColor:"rgba(38,192,211,0.2)"}},S={Success:"Success",Failure:"Failure",Warning:"Warning",Info:"Info"},J={ID:"NotiflixReportWrap",className:"notiflix-report",width:"320px",backgroundColor:"#f8f8f8",borderRadius:"25px",rtl:!1,zindex:4002,backOverlay:!0,backOverlayColor:"rgba(0,0,0,0.5)",backOverlayClickToClose:!1,fontFamily:"Quicksand",svgSize:"110px",plainText:!0,titleFontSize:"16px",titleMaxLength:34,messageFontSize:"13px",messageMaxLength:400,buttonFontSize:"14px",buttonMaxLength:34,cssAnimation:!0,cssAnimationDuration:360,cssAnimationStyle:"fade",success:{svgColor:"#32c682",titleColor:"#1e1e1e",messageColor:"#242424",buttonBackground:"#32c682",buttonColor:"#fff",backOverlayColor:"rgba(50,198,130,0.2)"},failure:{svgColor:"#ff5549",titleColor:"#1e1e1e",messageColor:"#242424",buttonBackground:"#ff5549",buttonColor:"#fff",backOverlayColor:"rgba(255,85,73,0.2)"},warning:{svgColor:"#eebf31",titleColor:"#1e1e1e",messageColor:"#242424",buttonBackground:"#eebf31",buttonColor:"#fff",backOverlayColor:"rgba(238,191,49,0.2)"},info:{svgColor:"#26c0d3",titleColor:"#1e1e1e",messageColor:"#242424",buttonBackground:"#26c0d3",buttonColor:"#fff",backOverlayColor:"rgba(38,192,211,0.2)"}},P={Show:"Show",Ask:"Ask",Prompt:"Prompt"},y={ID:"NotiflixConfirmWrap",className:"notiflix-confirm",width:"300px",zindex:4003,position:"center",distance:"10px",backgroundColor:"#f8f8f8",borderRadius:"25px",backOverlay:!0,backOverlayColor:"rgba(0,0,0,0.5)",rtl:!1,fontFamily:"Quicksand",cssAnimation:!0,cssAnimationDuration:300,cssAnimationStyle:"fade",plainText:!0,titleColor:"#32c682",titleFontSize:"16px",titleMaxLength:34,messageColor:"#1e1e1e",messageFontSize:"14px",messageMaxLength:110,buttonsFontSize:"15px",buttonsMaxLength:34,okButtonColor:"#f8f8f8",okButtonBackground:"#32c682",cancelButtonColor:"#f8f8f8",cancelButtonBackground:"#a9a9a9"},R={Standard:"Standard",Hourglass:"Hourglass",Circle:"Circle",Arrows:"Arrows",Dots:"Dots",Pulse:"Pulse",Custom:"Custom",Notiflix:"Notiflix"},H={ID:"NotiflixLoadingWrap",className:"notiflix-loading",zindex:4e3,backgroundColor:"rgba(0,0,0,0.8)",rtl:!1,fontFamily:"Quicksand",cssAnimation:!0,cssAnimationDuration:400,clickToClose:!1,customSvgUrl:null,customSvgCode:null,svgSize:"80px",svgColor:"#32c682",messageID:"NotiflixLoadingMessage",messageFontSize:"15px",messageMaxLength:34,messageColor:"#dcdcdc"},O={Standard:"Standard",Hourglass:"Hourglass",Circle:"Circle",Arrows:"Arrows",Dots:"Dots",Pulse:"Pulse"},T={ID:"NotiflixBlockWrap",querySelectorLimit:200,className:"notiflix-block",position:"absolute",zindex:1e3,backgroundColor:"rgba(255,255,255,0.9)",rtl:!1,fontFamily:"Quicksand",cssAnimation:!0,cssAnimationDuration:300,svgSize:"45px",svgColor:"#383838",messageFontSize:"14px",messageMaxLength:34,messageColor:"#383838"},j=function(t){return console.error("%c Notiflix Error ","padding:2px;border-radius:20px;color:#fff;background:#ff5549",`
`+t+x)},Lt=function(t){return console.log("%c Notiflix Info ","padding:2px;border-radius:20px;color:#fff;background:#26c0d3",`
`+t+x)},tt=function(t){return t||(t="head"),e.document[t]!==null||(j(`
Notiflix needs to be appended to the "<`+t+'>" element, but you called it before the "<'+t+'>" element has been created.'),!1)},nt=function(t,a){if(!tt("head"))return!1;if(t()!==null&&!e.document.getElementById(a)){var n=e.document.createElement("style");n.id=a,n.innerHTML=t(),e.document.head.appendChild(n)}},z=function(){var t={},a=!1,n=0;Object.prototype.toString.call(arguments[0])==="[object Boolean]"&&(a=arguments[0],n++);for(var m=function(p){for(var k in p)Object.prototype.hasOwnProperty.call(p,k)&&(t[k]=a&&Object.prototype.toString.call(p[k])==="[object Object]"?z(t[k],p[k]):p[k])};n<arguments.length;n++)m(arguments[n]);return t},_=function(t){var a=e.document.createElement("div");return a.innerHTML=t,a.textContent||a.innerText||""},qt=function(t,a){t||(t="110px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" id="NXReportSuccess" width="'+t+'" height="'+t+'" fill="'+a+'" viewBox="0 0 120 120"><style>@-webkit-keyframes NXReportSuccess1-animation{0%{-webkit-transform:translate(60px,57.7px) scale(.5,.5) translate(-60px,-57.7px);transform:translate(60px,57.7px) scale(.5,.5) translate(-60px,-57.7px)}50%,to{-webkit-transform:translate(60px,57.7px) scale(1,1) translate(-60px,-57.7px);transform:translate(60px,57.7px) scale(1,1) translate(-60px,-57.7px)}60%{-webkit-transform:translate(60px,57.7px) scale(.95,.95) translate(-60px,-57.7px);transform:translate(60px,57.7px) scale(.95,.95) translate(-60px,-57.7px)}}@keyframes NXReportSuccess1-animation{0%{-webkit-transform:translate(60px,57.7px) scale(.5,.5) translate(-60px,-57.7px);transform:translate(60px,57.7px) scale(.5,.5) translate(-60px,-57.7px)}50%,to{-webkit-transform:translate(60px,57.7px) scale(1,1) translate(-60px,-57.7px);transform:translate(60px,57.7px) scale(1,1) translate(-60px,-57.7px)}60%{-webkit-transform:translate(60px,57.7px) scale(.95,.95) translate(-60px,-57.7px);transform:translate(60px,57.7px) scale(.95,.95) translate(-60px,-57.7px)}}@-webkit-keyframes NXReportSuccess4-animation{0%{opacity:0}50%,to{opacity:1}}@keyframes NXReportSuccess4-animation{0%{opacity:0}50%,to{opacity:1}}@-webkit-keyframes NXReportSuccess3-animation{0%{opacity:0}40%,to{opacity:1}}@keyframes NXReportSuccess3-animation{0%{opacity:0}40%,to{opacity:1}}@-webkit-keyframes NXReportSuccess2-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@keyframes NXReportSuccess2-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}#NXReportSuccess *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g style="-webkit-animation-name:NXReportSuccess2-animation;animation-name:NXReportSuccess2-animation;-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)"><path d="M60 115.38C29.46 115.38 4.62 90.54 4.62 60 4.62 29.46 29.46 4.62 60 4.62c30.54 0 55.38 24.84 55.38 55.38 0 30.54-24.84 55.38-55.38 55.38zM60 0C26.92 0 0 26.92 0 60s26.92 60 60 60 60-26.92 60-60S93.08 0 60 0z" style="-webkit-animation-name:NXReportSuccess3-animation;animation-name:NXReportSuccess3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g><g style="-webkit-animation-name:NXReportSuccess1-animation;animation-name:NXReportSuccess1-animation;-webkit-transform:translate(60px,57.7px) scale(1,1) translate(-60px,-57.7px);-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)"><path d="M88.27 35.39L52.8 75.29 31.43 58.2c-.98-.81-2.44-.63-3.24.36-.79.99-.63 2.44.36 3.24l23.08 18.46c.43.34.93.51 1.44.51.64 0 1.27-.26 1.74-.78l36.91-41.53a2.3 2.3 0 0 0-.19-3.26c-.95-.86-2.41-.77-3.26.19z" style="-webkit-animation-name:NXReportSuccess4-animation;animation-name:NXReportSuccess4-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g></svg>';return n},Kt=function(t,a){t||(t="110px"),a||(a="#ff5549");var n='<svg xmlns="http://www.w3.org/2000/svg" id="NXReportFailure" width="'+t+'" height="'+t+'" fill="'+a+'" viewBox="0 0 120 120"><style>@-webkit-keyframes NXReportFailure2-animation{0%{opacity:0}40%,to{opacity:1}}@keyframes NXReportFailure2-animation{0%{opacity:0}40%,to{opacity:1}}@-webkit-keyframes NXReportFailure1-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@keyframes NXReportFailure1-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@-webkit-keyframes NXReportFailure3-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}50%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@keyframes NXReportFailure3-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}50%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@-webkit-keyframes NXReportFailure4-animation{0%{opacity:0}50%,to{opacity:1}}@keyframes NXReportFailure4-animation{0%{opacity:0}50%,to{opacity:1}}#NXReportFailure *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g style="-webkit-animation-name:NXReportFailure1-animation;animation-name:NXReportFailure1-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)"><path d="M4.35 34.95c0-16.82 13.78-30.6 30.6-30.6h50.1c16.82 0 30.6 13.78 30.6 30.6v50.1c0 16.82-13.78 30.6-30.6 30.6h-50.1c-16.82 0-30.6-13.78-30.6-30.6v-50.1zM34.95 120h50.1c19.22 0 34.95-15.73 34.95-34.95v-50.1C120 15.73 104.27 0 85.05 0h-50.1C15.73 0 0 15.73 0 34.95v50.1C0 104.27 15.73 120 34.95 120z" style="-webkit-animation-name:NXReportFailure2-animation;animation-name:NXReportFailure2-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g><g style="-webkit-animation-name:NXReportFailure3-animation;animation-name:NXReportFailure3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)"><path d="M82.4 37.6c-.9-.9-2.37-.9-3.27 0L60 56.73 40.86 37.6a2.306 2.306 0 0 0-3.26 3.26L56.73 60 37.6 79.13c-.9.9-.9 2.37 0 3.27.45.45 1.04.68 1.63.68.59 0 1.18-.23 1.63-.68L60 63.26 79.13 82.4c.45.45 1.05.68 1.64.68.58 0 1.18-.23 1.63-.68.9-.9.9-2.37 0-3.27L63.26 60 82.4 40.86c.9-.91.9-2.36 0-3.26z" style="-webkit-animation-name:NXReportFailure4-animation;animation-name:NXReportFailure4-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g></svg>';return n},Jt=function(t,a){t||(t="110px"),a||(a="#eebf31");var n='<svg xmlns="http://www.w3.org/2000/svg" id="NXReportWarning" width="'+t+'" height="'+t+'" fill="'+a+'" viewBox="0 0 120 120"><style>@-webkit-keyframes NXReportWarning2-animation{0%{opacity:0}40%,to{opacity:1}}@keyframes NXReportWarning2-animation{0%{opacity:0}40%,to{opacity:1}}@-webkit-keyframes NXReportWarning1-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@keyframes NXReportWarning1-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@-webkit-keyframes NXReportWarning3-animation{0%{-webkit-transform:translate(60px,66.6px) scale(.5,.5) translate(-60px,-66.6px);transform:translate(60px,66.6px) scale(.5,.5) translate(-60px,-66.6px)}50%,to{-webkit-transform:translate(60px,66.6px) scale(1,1) translate(-60px,-66.6px);transform:translate(60px,66.6px) scale(1,1) translate(-60px,-66.6px)}60%{-webkit-transform:translate(60px,66.6px) scale(.95,.95) translate(-60px,-66.6px);transform:translate(60px,66.6px) scale(.95,.95) translate(-60px,-66.6px)}}@keyframes NXReportWarning3-animation{0%{-webkit-transform:translate(60px,66.6px) scale(.5,.5) translate(-60px,-66.6px);transform:translate(60px,66.6px) scale(.5,.5) translate(-60px,-66.6px)}50%,to{-webkit-transform:translate(60px,66.6px) scale(1,1) translate(-60px,-66.6px);transform:translate(60px,66.6px) scale(1,1) translate(-60px,-66.6px)}60%{-webkit-transform:translate(60px,66.6px) scale(.95,.95) translate(-60px,-66.6px);transform:translate(60px,66.6px) scale(.95,.95) translate(-60px,-66.6px)}}@-webkit-keyframes NXReportWarning4-animation{0%{opacity:0}50%,to{opacity:1}}@keyframes NXReportWarning4-animation{0%{opacity:0}50%,to{opacity:1}}#NXReportWarning *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g style="-webkit-animation-name:NXReportWarning1-animation;animation-name:NXReportWarning1-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)"><path d="M115.46 106.15l-54.04-93.8c-.61-1.06-2.23-1.06-2.84 0l-54.04 93.8c-.62 1.07.21 2.29 1.42 2.29h108.08c1.21 0 2.04-1.22 1.42-2.29zM65.17 10.2l54.04 93.8c2.28 3.96-.65 8.78-5.17 8.78H5.96c-4.52 0-7.45-4.82-5.17-8.78l54.04-93.8c2.28-3.95 8.03-4 10.34 0z" style="-webkit-animation-name:NXReportWarning2-animation;animation-name:NXReportWarning2-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g><g style="-webkit-animation-name:NXReportWarning3-animation;animation-name:NXReportWarning3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform:translate(60px,66.6px) scale(1,1) translate(-60px,-66.6px)"><path d="M57.83 94.01c0 1.2.97 2.17 2.17 2.17s2.17-.97 2.17-2.17v-3.2c0-1.2-.97-2.17-2.17-2.17s-2.17.97-2.17 2.17v3.2zm0-14.15c0 1.2.97 2.17 2.17 2.17s2.17-.97 2.17-2.17V39.21c0-1.2-.97-2.17-2.17-2.17s-2.17.97-2.17 2.17v40.65z" style="-webkit-animation-name:NXReportWarning4-animation;animation-name:NXReportWarning4-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g></svg>';return n},Gt=function(t,a){t||(t="110px"),a||(a="#26c0d3");var n='<svg xmlns="http://www.w3.org/2000/svg" id="NXReportInfo" width="'+t+'" height="'+t+'" fill="'+a+'" viewBox="0 0 120 120"><style>@-webkit-keyframes NXReportInfo4-animation{0%{opacity:0}50%,to{opacity:1}}@keyframes NXReportInfo4-animation{0%{opacity:0}50%,to{opacity:1}}@-webkit-keyframes NXReportInfo3-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}50%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@keyframes NXReportInfo3-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}50%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@-webkit-keyframes NXReportInfo2-animation{0%{opacity:0}40%,to{opacity:1}}@keyframes NXReportInfo2-animation{0%{opacity:0}40%,to{opacity:1}}@-webkit-keyframes NXReportInfo1-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}@keyframes NXReportInfo1-animation{0%{-webkit-transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px);transform:translate(60px,60px) scale(.5,.5) translate(-60px,-60px)}40%,to{-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px);transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)}60%{-webkit-transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px);transform:translate(60px,60px) scale(.95,.95) translate(-60px,-60px)}}#NXReportInfo *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g style="-webkit-animation-name:NXReportInfo1-animation;animation-name:NXReportInfo1-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)"><path d="M60 115.38C29.46 115.38 4.62 90.54 4.62 60 4.62 29.46 29.46 4.62 60 4.62c30.54 0 55.38 24.84 55.38 55.38 0 30.54-24.84 55.38-55.38 55.38zM60 0C26.92 0 0 26.92 0 60s26.92 60 60 60 60-26.92 60-60S93.08 0 60 0z" style="-webkit-animation-name:NXReportInfo2-animation;animation-name:NXReportInfo2-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g><g style="-webkit-animation-name:NXReportInfo3-animation;animation-name:NXReportInfo3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform:translate(60px,60px) scale(1,1) translate(-60px,-60px)"><path d="M57.75 43.85c0-1.24 1.01-2.25 2.25-2.25s2.25 1.01 2.25 2.25v48.18c0 1.24-1.01 2.25-2.25 2.25s-2.25-1.01-2.25-2.25V43.85zm0-15.88c0-1.24 1.01-2.25 2.25-2.25s2.25 1.01 2.25 2.25v3.32c0 1.25-1.01 2.25-2.25 2.25s-2.25-1-2.25-2.25v-3.32z" style="-webkit-animation-name:NXReportInfo4-animation;animation-name:NXReportInfo4-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1)" fill="inherit" data-animator-group="true" data-animator-type="2"/></g></svg>';return n},St=function(t,a){t||(t="60px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" stroke="'+a+'" width="'+t+'" height="'+t+'" transform="scale(.8)" viewBox="0 0 38 38"><g fill="none" fill-rule="evenodd" stroke-width="2" transform="translate(1 1)"><circle cx="18" cy="18" r="18" stroke-opacity=".25"/><path d="M36 18c0-9.94-8.06-18-18-18"><animateTransform attributeName="transform" dur="1s" from="0 18 18" repeatCount="indefinite" to="360 18 18" type="rotate"/></path></g></svg>';return n},Rt=function(t,a){t||(t="60px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" id="NXLoadingHourglass" fill="'+a+'" width="'+t+'" height="'+t+'" viewBox="0 0 200 200"><style>@-webkit-keyframes NXhourglass5-animation{0%{-webkit-transform:scale(1,1);transform:scale(1,1)}16.67%{-webkit-transform:scale(1,.8);transform:scale(1,.8)}33.33%{-webkit-transform:scale(.88,.6);transform:scale(.88,.6)}37.5%{-webkit-transform:scale(.85,.55);transform:scale(.85,.55)}41.67%{-webkit-transform:scale(.8,.5);transform:scale(.8,.5)}45.83%{-webkit-transform:scale(.75,.45);transform:scale(.75,.45)}50%{-webkit-transform:scale(.7,.4);transform:scale(.7,.4)}54.17%{-webkit-transform:scale(.6,.35);transform:scale(.6,.35)}58.33%{-webkit-transform:scale(.5,.3);transform:scale(.5,.3)}83.33%,to{-webkit-transform:scale(.2,0);transform:scale(.2,0)}}@keyframes NXhourglass5-animation{0%{-webkit-transform:scale(1,1);transform:scale(1,1)}16.67%{-webkit-transform:scale(1,.8);transform:scale(1,.8)}33.33%{-webkit-transform:scale(.88,.6);transform:scale(.88,.6)}37.5%{-webkit-transform:scale(.85,.55);transform:scale(.85,.55)}41.67%{-webkit-transform:scale(.8,.5);transform:scale(.8,.5)}45.83%{-webkit-transform:scale(.75,.45);transform:scale(.75,.45)}50%{-webkit-transform:scale(.7,.4);transform:scale(.7,.4)}54.17%{-webkit-transform:scale(.6,.35);transform:scale(.6,.35)}58.33%{-webkit-transform:scale(.5,.3);transform:scale(.5,.3)}83.33%,to{-webkit-transform:scale(.2,0);transform:scale(.2,0)}}@-webkit-keyframes NXhourglass3-animation{0%{-webkit-transform:scale(1,.02);transform:scale(1,.02)}79.17%,to{-webkit-transform:scale(1,1);transform:scale(1,1)}}@keyframes NXhourglass3-animation{0%{-webkit-transform:scale(1,.02);transform:scale(1,.02)}79.17%,to{-webkit-transform:scale(1,1);transform:scale(1,1)}}@-webkit-keyframes NXhourglass1-animation{0%,83.33%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}@keyframes NXhourglass1-animation{0%,83.33%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(180deg);transform:rotate(180deg)}}#NXLoadingHourglass *{-webkit-animation-duration:1.2s;animation-duration:1.2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}</style><g data-animator-group="true" data-animator-type="1" style="-webkit-animation-name:NXhourglass1-animation;animation-name:NXhourglass1-animation;-webkit-transform-origin:50% 50%;transform-origin:50% 50%;transform-box:fill-box"><g id="NXhourglass2" fill="inherit"><g data-animator-group="true" data-animator-type="2" style="-webkit-animation-name:NXhourglass3-animation;animation-name:NXhourglass3-animation;-webkit-animation-timing-function:cubic-bezier(.42,0,.58,1);animation-timing-function:cubic-bezier(.42,0,.58,1);-webkit-transform-origin:50% 100%;transform-origin:50% 100%;transform-box:fill-box" opacity=".4"><path id="NXhourglass4" d="M100 100l-34.38 32.08v31.14h68.76v-31.14z"/></g><g data-animator-group="true" data-animator-type="2" style="-webkit-animation-name:NXhourglass5-animation;animation-name:NXhourglass5-animation;-webkit-transform-origin:50% 100%;transform-origin:50% 100%;transform-box:fill-box" opacity=".4"><path id="NXhourglass6" d="M100 100L65.62 67.92V36.78h68.76v31.14z"/></g><path d="M51.14 38.89h8.33v14.93c0 15.1 8.29 28.99 23.34 39.1 1.88 1.25 3.04 3.97 3.04 7.08s-1.16 5.83-3.04 7.09c-15.05 10.1-23.34 23.99-23.34 39.09v14.93h-8.33a4.859 4.859 0 1 0 0 9.72h97.72a4.859 4.859 0 1 0 0-9.72h-8.33v-14.93c0-15.1-8.29-28.99-23.34-39.09-1.88-1.26-3.04-3.98-3.04-7.09s1.16-5.83 3.04-7.08c15.05-10.11 23.34-24 23.34-39.1V38.89h8.33a4.859 4.859 0 1 0 0-9.72H51.14a4.859 4.859 0 1 0 0 9.72zm79.67 14.93c0 15.87-11.93 26.25-19.04 31.03-4.6 3.08-7.34 8.75-7.34 15.15 0 6.41 2.74 12.07 7.34 15.15 7.11 4.78 19.04 15.16 19.04 31.03v14.93H69.19v-14.93c0-15.87 11.93-26.25 19.04-31.02 4.6-3.09 7.34-8.75 7.34-15.16 0-6.4-2.74-12.07-7.34-15.15-7.11-4.78-19.04-15.16-19.04-31.03V38.89h61.62v14.93z"/></g></g></svg>';return n},Mt=function(t,a){t||(t="60px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" width="'+t+'" height="'+t+'" viewBox="25 25 50 50" style="-webkit-animation:rotate 2s linear infinite;animation:rotate 2s linear infinite;height:'+t+";-webkit-transform-origin:center center;-ms-transform-origin:center center;transform-origin:center center;width:"+t+';position:absolute;top:0;left:0;margin:auto"><style>@-webkit-keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotate{to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}to{stroke-dasharray:89,200;stroke-dashoffset:-124}}@keyframes dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}to{stroke-dasharray:89,200;stroke-dashoffset:-124}}</style><circle cx="50" cy="50" r="20" fill="none" stroke="'+a+'" stroke-width="2" style="-webkit-animation:dash 1.5s ease-in-out infinite,color 1.5s ease-in-out infinite;animation:dash 1.5s ease-in-out infinite,color 1.5s ease-in-out infinite" stroke-dasharray="150 200" stroke-dashoffset="-10" stroke-linecap="round"/></svg>';return n},At=function(t,a){t||(t="60px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" fill="'+a+'" width="'+t+'" height="'+t+'" viewBox="0 0 128 128"><g><path fill="inherit" d="M109.25 55.5h-36l12-12a29.54 29.54 0 0 0-49.53 12H18.75A46.04 46.04 0 0 1 96.9 31.84l12.35-12.34v36zm-90.5 17h36l-12 12a29.54 29.54 0 0 0 49.53-12h16.97A46.04 46.04 0 0 1 31.1 96.16L18.74 108.5v-36z"/><animateTransform attributeName="transform" dur="1.5s" from="0 64 64" repeatCount="indefinite" to="360 64 64" type="rotate"/></g></svg>';return n},Et=function(t,a){t||(t="60px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" fill="'+a+'" width="'+t+'" height="'+t+'" viewBox="0 0 100 100"><g transform="translate(25 50)"><circle r="9" fill="inherit" transform="scale(.239)"><animateTransform attributeName="transform" begin="-0.266s" calcMode="spline" dur="0.8s" keySplines="0.3 0 0.7 1;0.3 0 0.7 1" keyTimes="0;0.5;1" repeatCount="indefinite" type="scale" values="0;1;0"/></circle></g><g transform="translate(50 50)"><circle r="9" fill="inherit" transform="scale(.00152)"><animateTransform attributeName="transform" begin="-0.133s" calcMode="spline" dur="0.8s" keySplines="0.3 0 0.7 1;0.3 0 0.7 1" keyTimes="0;0.5;1" repeatCount="indefinite" type="scale" values="0;1;0"/></circle></g><g transform="translate(75 50)"><circle r="9" fill="inherit" transform="scale(.299)"><animateTransform attributeName="transform" begin="0s" calcMode="spline" dur="0.8s" keySplines="0.3 0 0.7 1;0.3 0 0.7 1" keyTimes="0;0.5;1" repeatCount="indefinite" type="scale" values="0;1;0"/></circle></g></svg>';return n},Bt=function(t,a){t||(t="60px"),a||(a="#32c682");var n='<svg xmlns="http://www.w3.org/2000/svg" stroke="'+a+'" width="'+t+'" height="'+t+'" viewBox="0 0 44 44"><g fill="none" fill-rule="evenodd" stroke-width="2"><circle cx="22" cy="22" r="1"><animate attributeName="r" begin="0s" calcMode="spline" dur="1.8s" keySplines="0.165, 0.84, 0.44, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 20"/><animate attributeName="stroke-opacity" begin="0s" calcMode="spline" dur="1.8s" keySplines="0.3, 0.61, 0.355, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 0"/></circle><circle cx="22" cy="22" r="1"><animate attributeName="r" begin="-0.9s" calcMode="spline" dur="1.8s" keySplines="0.165, 0.84, 0.44, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 20"/><animate attributeName="stroke-opacity" begin="-0.9s" calcMode="spline" dur="1.8s" keySplines="0.3, 0.61, 0.355, 1" keyTimes="0; 1" repeatCount="indefinite" values="1; 0"/></circle></g></svg>';return n},Yt=function(t,a,n){t||(t="60px"),a||(a="#f8f8f8"),n||(n="#32c682");var m='<svg xmlns="http://www.w3.org/2000/svg" id="NXLoadingNotiflixLib" width="'+t+'" height="'+t+'" viewBox="0 0 200 200"><defs><style>@keyframes notiflix-n{0%{stroke-dashoffset:1000}to{stroke-dashoffset:0}}@keyframes notiflix-x{0%{stroke-dashoffset:1000}to{stroke-dashoffset:0}}@keyframes notiflix-dot{0%,to{stroke-width:0}50%{stroke-width:12}}.nx-icon-line{stroke:'+a+';stroke-width:12;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22;fill:none}</style></defs><path d="M47.97 135.05a6.5 6.5 0 1 1 0 13 6.5 6.5 0 0 1 0-13z" style="animation-name:notiflix-dot;animation-timing-function:ease-in-out;animation-duration:1.25s;animation-iteration-count:infinite;animation-direction:normal" fill="'+n+'" stroke="'+n+'" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="22" stroke-width="12"/><path class="nx-icon-line" d="M10.14 144.76V87.55c0-5.68-4.54-41.36 37.83-41.36 42.36 0 37.82 35.68 37.82 41.36v57.21" style="animation-name:notiflix-n;animation-timing-function:linear;animation-duration:2.5s;animation-delay:0s;animation-iteration-count:infinite;animation-direction:normal" stroke-dasharray="500"/><path class="nx-icon-line" d="M115.06 144.49c24.98-32.68 49.96-65.35 74.94-98.03M114.89 46.6c25.09 32.58 50.19 65.17 75.29 97.75" style="animation-name:notiflix-x;animation-timing-function:linear;animation-duration:2.5s;animation-delay:.2s;animation-iteration-count:infinite;animation-direction:normal" stroke-dasharray="500"/></svg>';return m},Zt=function(){return'[id^=NotiflixNotifyWrap]{pointer-events:none;position:fixed;z-index:4001;opacity:1;right:10px;top:10px;width:280px;max-width:96%;-webkit-box-sizing:border-box;box-sizing:border-box;background:transparent}[id^=NotiflixNotifyWrap].nx-flex-center-center{max-height:calc(100vh - 20px);overflow-x:hidden;overflow-y:auto;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;margin:auto}[id^=NotiflixNotifyWrap]::-webkit-scrollbar{width:0;height:0}[id^=NotiflixNotifyWrap]::-webkit-scrollbar-thumb{background:transparent}[id^=NotiflixNotifyWrap]::-webkit-scrollbar-track{background:transparent}[id^=NotiflixNotifyWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixNotifyOverlay]{-webkit-transition:background .3s ease-in-out;-o-transition:background .3s ease-in-out;transition:background .3s ease-in-out}[id^=NotiflixNotifyWrap]>div{pointer-events:all;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;font-family:"Quicksand",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;width:100%;display:-webkit-inline-box;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;position:relative;margin:0 0 10px;border-radius:5px;background:#1e1e1e;color:#fff;padding:10px 12px;font-size:14px;line-height:1.4}[id^=NotiflixNotifyWrap]>div:last-child{margin:0}[id^=NotiflixNotifyWrap]>div.nx-with-callback{cursor:pointer}[id^=NotiflixNotifyWrap]>div.nx-with-icon{padding:8px;min-height:56px}[id^=NotiflixNotifyWrap]>div.nx-paused{cursor:auto}[id^=NotiflixNotifyWrap]>div.nx-notify-click-to-close{cursor:pointer}[id^=NotiflixNotifyWrap]>div.nx-with-close-button{padding:10px 36px 10px 12px}[id^=NotiflixNotifyWrap]>div.nx-with-icon.nx-with-close-button{padding:6px 36px 6px 6px}[id^=NotiflixNotifyWrap]>div>span.nx-message{cursor:inherit;font-weight:normal;font-family:inherit!important;word-break:break-all;word-break:break-word}[id^=NotiflixNotifyWrap]>div>span.nx-close-button{cursor:pointer;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out;position:absolute;right:8px;top:0;bottom:0;margin:auto;color:inherit;width:20px;height:20px}[id^=NotiflixNotifyWrap]>div>span.nx-close-button:hover{-webkit-transform:rotate(90deg);transform:rotate(90deg)}[id^=NotiflixNotifyWrap]>div>span.nx-close-button>svg{position:absolute;width:16px;height:16px;right:2px;top:2px}[id^=NotiflixNotifyWrap]>div>.nx-message-icon{position:absolute;width:40px;height:40px;font-size:30px;line-height:40px;text-align:center;left:8px;top:0;bottom:0;margin:auto;border-radius:inherit}[id^=NotiflixNotifyWrap]>div>.nx-message-icon-fa.nx-message-icon-fa-shadow{color:inherit;background:rgba(0,0,0,.15);-webkit-box-shadow:inset 0 0 34px rgba(0,0,0,.2);box-shadow:inset 0 0 34px rgba(0,0,0,.2);text-shadow:0 0 10px rgba(0,0,0,.3)}[id^=NotiflixNotifyWrap]>div>span.nx-with-icon{position:relative;float:left;width:calc(100% - 40px);margin:0 0 0 40px;padding:0 0 0 10px;-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixNotifyWrap]>div.nx-rtl-on>.nx-message-icon{left:auto;right:8px}[id^=NotiflixNotifyWrap]>div.nx-rtl-on>span.nx-with-icon{padding:0 10px 0 0;margin:0 40px 0 0}[id^=NotiflixNotifyWrap]>div.nx-rtl-on>span.nx-close-button{right:auto;left:8px}[id^=NotiflixNotifyWrap]>div.nx-with-icon.nx-with-close-button.nx-rtl-on{padding:6px 6px 6px 36px}[id^=NotiflixNotifyWrap]>div.nx-with-close-button.nx-rtl-on{padding:10px 12px 10px 36px}[id^=NotiflixNotifyOverlay].nx-with-animation,[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-fade{-webkit-animation:notify-animation-fade .3s ease-in-out 0s normal;animation:notify-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes notify-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes notify-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-zoom{-webkit-animation:notify-animation-zoom .3s ease-in-out 0s normal;animation:notify-animation-zoom .3s ease-in-out 0s normal}@-webkit-keyframes notify-animation-zoom{0%{-webkit-transform:scale(0);transform:scale(0)}50%{-webkit-transform:scale(1.05);transform:scale(1.05)}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes notify-animation-zoom{0%{-webkit-transform:scale(0);transform:scale(0)}50%{-webkit-transform:scale(1.05);transform:scale(1.05)}100%{-webkit-transform:scale(1);transform:scale(1)}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-right{-webkit-animation:notify-animation-from-right .3s ease-in-out 0s normal;animation:notify-animation-from-right .3s ease-in-out 0s normal}@-webkit-keyframes notify-animation-from-right{0%{right:-300px;opacity:0}50%{right:8px;opacity:1}100%{right:0;opacity:1}}@keyframes notify-animation-from-right{0%{right:-300px;opacity:0}50%{right:8px;opacity:1}100%{right:0;opacity:1}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-left{-webkit-animation:notify-animation-from-left .3s ease-in-out 0s normal;animation:notify-animation-from-left .3s ease-in-out 0s normal}@-webkit-keyframes notify-animation-from-left{0%{left:-300px;opacity:0}50%{left:8px;opacity:1}100%{left:0;opacity:1}}@keyframes notify-animation-from-left{0%{left:-300px;opacity:0}50%{left:8px;opacity:1}100%{left:0;opacity:1}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-top{-webkit-animation:notify-animation-from-top .3s ease-in-out 0s normal;animation:notify-animation-from-top .3s ease-in-out 0s normal}@-webkit-keyframes notify-animation-from-top{0%{top:-50px;opacity:0}50%{top:8px;opacity:1}100%{top:0;opacity:1}}@keyframes notify-animation-from-top{0%{top:-50px;opacity:0}50%{top:8px;opacity:1}100%{top:0;opacity:1}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-bottom{-webkit-animation:notify-animation-from-bottom .3s ease-in-out 0s normal;animation:notify-animation-from-bottom .3s ease-in-out 0s normal}@-webkit-keyframes notify-animation-from-bottom{0%{bottom:-50px;opacity:0}50%{bottom:8px;opacity:1}100%{bottom:0;opacity:1}}@keyframes notify-animation-from-bottom{0%{bottom:-50px;opacity:0}50%{bottom:8px;opacity:1}100%{bottom:0;opacity:1}}[id^=NotiflixNotifyOverlay].nx-with-animation.nx-remove,[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-fade.nx-remove{opacity:0;-webkit-animation:notify-remove-fade .3s ease-in-out 0s normal;animation:notify-remove-fade .3s ease-in-out 0s normal}@-webkit-keyframes notify-remove-fade{0%{opacity:1}100%{opacity:0}}@keyframes notify-remove-fade{0%{opacity:1}100%{opacity:0}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-zoom.nx-remove{-webkit-transform:scale(0);transform:scale(0);-webkit-animation:notify-remove-zoom .3s ease-in-out 0s normal;animation:notify-remove-zoom .3s ease-in-out 0s normal}@-webkit-keyframes notify-remove-zoom{0%{-webkit-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(1.05);transform:scale(1.05)}100%{-webkit-transform:scale(0);transform:scale(0)}}@keyframes notify-remove-zoom{0%{-webkit-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(1.05);transform:scale(1.05)}100%{-webkit-transform:scale(0);transform:scale(0)}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-top.nx-remove{opacity:0;-webkit-animation:notify-remove-to-top .3s ease-in-out 0s normal;animation:notify-remove-to-top .3s ease-in-out 0s normal}@-webkit-keyframes notify-remove-to-top{0%{top:0;opacity:1}50%{top:8px;opacity:1}100%{top:-50px;opacity:0}}@keyframes notify-remove-to-top{0%{top:0;opacity:1}50%{top:8px;opacity:1}100%{top:-50px;opacity:0}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-right.nx-remove{opacity:0;-webkit-animation:notify-remove-to-right .3s ease-in-out 0s normal;animation:notify-remove-to-right .3s ease-in-out 0s normal}@-webkit-keyframes notify-remove-to-right{0%{right:0;opacity:1}50%{right:8px;opacity:1}100%{right:-300px;opacity:0}}@keyframes notify-remove-to-right{0%{right:0;opacity:1}50%{right:8px;opacity:1}100%{right:-300px;opacity:0}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-bottom.nx-remove{opacity:0;-webkit-animation:notify-remove-to-bottom .3s ease-in-out 0s normal;animation:notify-remove-to-bottom .3s ease-in-out 0s normal}@-webkit-keyframes notify-remove-to-bottom{0%{bottom:0;opacity:1}50%{bottom:8px;opacity:1}100%{bottom:-50px;opacity:0}}@keyframes notify-remove-to-bottom{0%{bottom:0;opacity:1}50%{bottom:8px;opacity:1}100%{bottom:-50px;opacity:0}}[id^=NotiflixNotifyWrap]>div.nx-with-animation.nx-from-left.nx-remove{opacity:0;-webkit-animation:notify-remove-to-left .3s ease-in-out 0s normal;animation:notify-remove-to-left .3s ease-in-out 0s normal}@-webkit-keyframes notify-remove-to-left{0%{left:0;opacity:1}50%{left:8px;opacity:1}100%{left:-300px;opacity:0}}@keyframes notify-remove-to-left{0%{left:0;opacity:1}50%{left:8px;opacity:1}100%{left:-300px;opacity:0}}'},ct=0,mt=function(t,a,n,m){if(!tt("body"))return!1;i||X.Notify.init({});var p=z(!0,i,{});if(typeof n=="object"&&!Array.isArray(n)||typeof m=="object"&&!Array.isArray(m)){var k={};typeof n=="object"?k=n:typeof m=="object"&&(k=m),i=z(!0,i,k)}var u=i[t.toLocaleLowerCase("en")];ct++,typeof a!="string"&&(a="Notiflix "+t),i.plainText&&(a=_(a)),!i.plainText&&a.length>i.messageMaxLength&&(i=z(!0,i,{closeButton:!0,messageMaxLength:150}),a='Possible HTML Tags Error: The "plainText" option is "false" and the notification content length is more than the "messageMaxLength" option.'),a.length>i.messageMaxLength&&(a=a.substring(0,i.messageMaxLength)+"..."),i.fontAwesomeIconStyle==="shadow"&&(u.fontAwesomeIconColor=u.background),i.cssAnimation||(i.cssAnimationDuration=0);var l=e.document.getElementById(d.wrapID)||e.document.createElement("div");if(l.id=d.wrapID,l.style.width=i.width,l.style.zIndex=i.zindex,l.style.opacity=i.opacity,i.position==="center-center"?(l.style.left=i.distance,l.style.top=i.distance,l.style.right=i.distance,l.style.bottom=i.distance,l.style.margin="auto",l.classList.add("nx-flex-center-center"),l.style.maxHeight="calc((100vh - "+i.distance+") - "+i.distance+")",l.style.display="flex",l.style.flexWrap="wrap",l.style.flexDirection="column",l.style.justifyContent="center",l.style.alignItems="center",l.style.pointerEvents="none"):i.position==="center-top"?(l.style.left=i.distance,l.style.right=i.distance,l.style.top=i.distance,l.style.bottom="auto",l.style.margin="auto"):i.position==="center-bottom"?(l.style.left=i.distance,l.style.right=i.distance,l.style.bottom=i.distance,l.style.top="auto",l.style.margin="auto"):i.position==="right-bottom"?(l.style.right=i.distance,l.style.bottom=i.distance,l.style.top="auto",l.style.left="auto"):i.position==="left-top"?(l.style.left=i.distance,l.style.top=i.distance,l.style.right="auto",l.style.bottom="auto"):i.position==="left-bottom"?(l.style.left=i.distance,l.style.bottom=i.distance,l.style.top="auto",l.style.right="auto"):(l.style.right=i.distance,l.style.top=i.distance,l.style.left="auto",l.style.bottom="auto"),i.backOverlay){var w=e.document.getElementById(d.overlayID)||e.document.createElement("div");w.id=d.overlayID,w.style.width="100%",w.style.height="100%",w.style.position="fixed",w.style.zIndex=i.zindex-1,w.style.left=0,w.style.top=0,w.style.right=0,w.style.bottom=0,w.style.background=u.backOverlayColor||i.backOverlayColor,w.className=i.cssAnimation?"nx-with-animation":"",w.style.animationDuration=i.cssAnimation?i.cssAnimationDuration+"ms":"",e.document.getElementById(d.overlayID)||e.document.body.appendChild(w)}e.document.getElementById(d.wrapID)||e.document.body.appendChild(l);var f=e.document.createElement("div");f.id=i.ID+"-"+ct,f.className=i.className+" "+u.childClassName+" "+(i.cssAnimation?"nx-with-animation":"")+" "+(i.useIcon?"nx-with-icon":"")+" nx-"+i.cssAnimationStyle+" "+(i.closeButton&&typeof n!="function"?"nx-with-close-button":"")+" "+(typeof n=="function"?"nx-with-callback":"")+" "+(i.clickToClose?"nx-notify-click-to-close":""),f.style.fontSize=i.fontSize,f.style.color=u.textColor,f.style.background=u.background,f.style.borderRadius=i.borderRadius,f.style.pointerEvents="all",i.rtl&&(f.setAttribute("dir","rtl"),f.classList.add("nx-rtl-on")),f.style.fontFamily='"'+i.fontFamily+'", '+b,i.cssAnimation&&(f.style.animationDuration=i.cssAnimationDuration+"ms");var L="";if(i.closeButton&&typeof n!="function"&&(L='<span class="nx-close-button"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><g><path fill="'+u.notiflixIconColor+'" d="M0.38 2.19l7.8 7.81 -7.8 7.81c-0.51,0.5 -0.51,1.31 -0.01,1.81 0.25,0.25 0.57,0.38 0.91,0.38 0.34,0 0.67,-0.14 0.91,-0.38l7.81 -7.81 7.81 7.81c0.24,0.24 0.57,0.38 0.91,0.38 0.34,0 0.66,-0.14 0.9,-0.38 0.51,-0.5 0.51,-1.31 0,-1.81l-7.81 -7.81 7.81 -7.81c0.51,-0.5 0.51,-1.31 0,-1.82 -0.5,-0.5 -1.31,-0.5 -1.81,0l-7.81 7.81 -7.81 -7.81c-0.5,-0.5 -1.31,-0.5 -1.81,0 -0.51,0.51 -0.51,1.32 0,1.82z"/></g></svg></span>'),!i.useIcon)f.innerHTML='<span class="nx-message">'+a+"</span>"+(i.closeButton?L:"");else if(i.useFontAwesome)f.innerHTML='<i style="color:'+u.fontAwesomeIconColor+"; font-size:"+i.fontAwesomeIconSize+';" class="nx-message-icon nx-message-icon-fa '+u.fontAwesomeClassName+" "+(i.fontAwesomeIconStyle==="shadow"?"nx-message-icon-fa-shadow":"nx-message-icon-fa-basic")+'"></i><span class="nx-message nx-with-icon">'+a+"</span>"+(i.closeButton?L:"");else{var E="";t===N.Success?E='<svg class="nx-message-icon" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><g><path fill="'+u.notiflixIconColor+'" d="M20 0c11.03,0 20,8.97 20,20 0,11.03 -8.97,20 -20,20 -11.03,0 -20,-8.97 -20,-20 0,-11.03 8.97,-20 20,-20zm0 37.98c9.92,0 17.98,-8.06 17.98,-17.98 0,-9.92 -8.06,-17.98 -17.98,-17.98 -9.92,0 -17.98,8.06 -17.98,17.98 0,9.92 8.06,17.98 17.98,17.98zm-2.4 -13.29l11.52 -12.96c0.37,-0.41 1.01,-0.45 1.42,-0.08 0.42,0.37 0.46,1 0.09,1.42l-12.16 13.67c-0.19,0.22 -0.46,0.34 -0.75,0.34 -0.23,0 -0.45,-0.07 -0.63,-0.22l-7.6 -6.07c-0.43,-0.35 -0.5,-0.99 -0.16,-1.42 0.35,-0.43 0.99,-0.5 1.42,-0.16l6.85 5.48z"/></g></svg>':t===N.Failure?E='<svg class="nx-message-icon" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><g><path fill="'+u.notiflixIconColor+'" d="M20 0c11.03,0 20,8.97 20,20 0,11.03 -8.97,20 -20,20 -11.03,0 -20,-8.97 -20,-20 0,-11.03 8.97,-20 20,-20zm0 37.98c9.92,0 17.98,-8.06 17.98,-17.98 0,-9.92 -8.06,-17.98 -17.98,-17.98 -9.92,0 -17.98,8.06 -17.98,17.98 0,9.92 8.06,17.98 17.98,17.98zm1.42 -17.98l6.13 6.12c0.39,0.4 0.39,1.04 0,1.43 -0.19,0.19 -0.45,0.29 -0.71,0.29 -0.27,0 -0.53,-0.1 -0.72,-0.29l-6.12 -6.13 -6.13 6.13c-0.19,0.19 -0.44,0.29 -0.71,0.29 -0.27,0 -0.52,-0.1 -0.71,-0.29 -0.39,-0.39 -0.39,-1.03 0,-1.43l6.13 -6.12 -6.13 -6.13c-0.39,-0.39 -0.39,-1.03 0,-1.42 0.39,-0.39 1.03,-0.39 1.42,0l6.13 6.12 6.12 -6.12c0.4,-0.39 1.04,-0.39 1.43,0 0.39,0.39 0.39,1.03 0,1.42l-6.13 6.13z"/></g></svg>':t===N.Warning?E='<svg class="nx-message-icon" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><g><path fill="'+u.notiflixIconColor+'" d="M21.91 3.48l17.8 30.89c0.84,1.46 -0.23,3.25 -1.91,3.25l-35.6 0c-1.68,0 -2.75,-1.79 -1.91,-3.25l17.8 -30.89c0.85,-1.47 2.97,-1.47 3.82,0zm16.15 31.84l-17.8 -30.89c-0.11,-0.2 -0.41,-0.2 -0.52,0l-17.8 30.89c-0.12,0.2 0.05,0.4 0.26,0.4l35.6 0c0.21,0 0.38,-0.2 0.26,-0.4zm-19.01 -4.12l0 -1.05c0,-0.53 0.42,-0.95 0.95,-0.95 0.53,0 0.95,0.42 0.95,0.95l0 1.05c0,0.53 -0.42,0.95 -0.95,0.95 -0.53,0 -0.95,-0.42 -0.95,-0.95zm0 -4.66l0 -13.39c0,-0.52 0.42,-0.95 0.95,-0.95 0.53,0 0.95,0.43 0.95,0.95l0 13.39c0,0.53 -0.42,0.96 -0.95,0.96 -0.53,0 -0.95,-0.43 -0.95,-0.96z"/></g></svg>':t===N.Info&&(E='<svg class="nx-message-icon" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><g><path fill="'+u.notiflixIconColor+'" d="M20 0c11.03,0 20,8.97 20,20 0,11.03 -8.97,20 -20,20 -11.03,0 -20,-8.97 -20,-20 0,-11.03 8.97,-20 20,-20zm0 37.98c9.92,0 17.98,-8.06 17.98,-17.98 0,-9.92 -8.06,-17.98 -17.98,-17.98 -9.92,0 -17.98,8.06 -17.98,17.98 0,9.92 8.06,17.98 17.98,17.98zm-0.99 -23.3c0,-0.54 0.44,-0.98 0.99,-0.98 0.55,0 0.99,0.44 0.99,0.98l0 15.86c0,0.55 -0.44,0.99 -0.99,0.99 -0.55,0 -0.99,-0.44 -0.99,-0.99l0 -15.86zm0 -5.22c0,-0.55 0.44,-0.99 0.99,-0.99 0.55,0 0.99,0.44 0.99,0.99l0 1.09c0,0.54 -0.44,0.99 -0.99,0.99 -0.55,0 -0.99,-0.45 -0.99,-0.99l0 -1.09z"/></g></svg>'),f.innerHTML=E+'<span class="nx-message nx-with-icon">'+a+"</span>"+(i.closeButton?L:"")}if(i.position==="left-bottom"||i.position==="right-bottom"){var I=e.document.getElementById(d.wrapID);I.insertBefore(f,I.firstChild)}else e.document.getElementById(d.wrapID).appendChild(f);var A=e.document.getElementById(f.id);if(A){var v,W,C=function(){A.classList.add("nx-remove");var g=e.document.getElementById(d.overlayID);g&&0>=l.childElementCount&&g.classList.add("nx-remove"),clearTimeout(v)},$=function(){if(A&&A.parentNode!==null&&A.parentNode.removeChild(A),0>=l.childElementCount&&l.parentNode!==null){l.parentNode.removeChild(l);var g=e.document.getElementById(d.overlayID);g&&g.parentNode!==null&&g.parentNode.removeChild(g)}clearTimeout(W)};if(i.closeButton&&typeof n!="function"){var Q=e.document.getElementById(f.id).querySelector("span.nx-close-button");Q.addEventListener("click",function(){C();var g=setTimeout(function(){$(),clearTimeout(g)},i.cssAnimationDuration)})}if((typeof n=="function"||i.clickToClose)&&A.addEventListener("click",function(){typeof n=="function"&&n(),C();var g=setTimeout(function(){$(),clearTimeout(g)},i.cssAnimationDuration)}),!i.closeButton&&typeof n!="function"){var F=function(){v=setTimeout(function(){C()},i.timeout),W=setTimeout(function(){$()},i.timeout+i.cssAnimationDuration)};F(),i.pauseOnHover&&(A.addEventListener("mouseenter",function(){A.classList.add("nx-paused"),clearTimeout(v),clearTimeout(W)}),A.addEventListener("mouseleave",function(){A.classList.remove("nx-paused"),F()}))}}if(i.showOnlyTheLastOne&&0<ct)for(var U,M=e.document.querySelectorAll("[id^="+i.ID+"-]:not([id="+i.ID+"-"+ct+"])"),K=0;K<M.length;K++)U=M[K],U.parentNode!==null&&U.parentNode.removeChild(U);i=z(!0,i,p)},te=function(){return'[id^=NotiflixReportWrap]{position:fixed;z-index:4002;width:100%;height:100%;-webkit-box-sizing:border-box;box-sizing:border-box;font-family:"Quicksand",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;left:0;top:0;padding:10px;color:#1e1e1e;border-radius:25px;background:transparent;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}[id^=NotiflixReportWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixReportWrap]>div[class*="-overlay"]{width:100%;height:100%;left:0;top:0;background:rgba(255,255,255,.5);position:fixed;z-index:0}[id^=NotiflixReportWrap]>div.nx-report-click-to-close{cursor:pointer}[id^=NotiflixReportWrap]>div[class*="-content"]{width:320px;max-width:100%;max-height:96vh;overflow-x:hidden;overflow-y:auto;border-radius:inherit;padding:10px;-webkit-filter:drop-shadow(0 0 5px rgba(0,0,0,0.05));filter:drop-shadow(0 0 5px rgba(0, 0, 0, .05));border:1px solid rgba(0,0,0,.03);background:#f8f8f8;position:relative;z-index:1}[id^=NotiflixReportWrap]>div[class*="-content"]::-webkit-scrollbar{width:0;height:0}[id^=NotiflixReportWrap]>div[class*="-content"]::-webkit-scrollbar-thumb{background:transparent}[id^=NotiflixReportWrap]>div[class*="-content"]::-webkit-scrollbar-track{background:transparent}[id^=NotiflixReportWrap]>div[class*="-content"]>div[class$="-icon"]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:110px;height:110px;display:block;margin:6px auto 12px}[id^=NotiflixReportWrap]>div[class*="-content"]>div[class$="-icon"] svg{min-width:100%;max-width:100%;height:auto}[id^=NotiflixReportWrap]>*>h5{word-break:break-all;word-break:break-word;font-family:inherit!important;font-size:16px;font-weight:500;line-height:1.4;margin:0 0 10px;padding:0 0 10px;border-bottom:1px solid rgba(0,0,0,.1);float:left;width:100%;text-align:center}[id^=NotiflixReportWrap]>*>p{word-break:break-all;word-break:break-word;font-family:inherit!important;font-size:13px;line-height:1.4;font-weight:normal;float:left;width:100%;padding:0 10px;margin:0 0 10px}[id^=NotiflixReportWrap] a#NXReportButton{word-break:break-all;word-break:break-word;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;font-family:inherit!important;-webkit-transition:all .25s ease-in-out;-o-transition:all .25s ease-in-out;transition:all .25s ease-in-out;cursor:pointer;float:right;padding:7px 17px;background:#32c682;font-size:14px;line-height:1.4;font-weight:500;border-radius:inherit!important;color:#fff}[id^=NotiflixReportWrap] a#NXReportButton:hover{-webkit-box-shadow:inset 0 -60px 5px -5px rgba(0,0,0,.25);box-shadow:inset 0 -60px 5px -5px rgba(0,0,0,.25)}[id^=NotiflixReportWrap].nx-rtl-on a#NXReportButton{float:left}[id^=NotiflixReportWrap]>div[class*="-overlay"].nx-with-animation{-webkit-animation:report-overlay-animation .3s ease-in-out 0s normal;animation:report-overlay-animation .3s ease-in-out 0s normal}@-webkit-keyframes report-overlay-animation{0%{opacity:0}100%{opacity:1}}@keyframes report-overlay-animation{0%{opacity:0}100%{opacity:1}}[id^=NotiflixReportWrap]>div[class*="-content"].nx-with-animation.nx-fade{-webkit-animation:report-animation-fade .3s ease-in-out 0s normal;animation:report-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes report-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes report-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixReportWrap]>div[class*="-content"].nx-with-animation.nx-zoom{-webkit-animation:report-animation-zoom .3s ease-in-out 0s normal;animation:report-animation-zoom .3s ease-in-out 0s normal}@-webkit-keyframes report-animation-zoom{0%{opacity:0;-webkit-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}@keyframes report-animation-zoom{0%{opacity:0;-webkit-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}[id^=NotiflixReportWrap].nx-remove>div[class*="-overlay"].nx-with-animation{opacity:0;-webkit-animation:report-overlay-animation-remove .3s ease-in-out 0s normal;animation:report-overlay-animation-remove .3s ease-in-out 0s normal}@-webkit-keyframes report-overlay-animation-remove{0%{opacity:1}100%{opacity:0}}@keyframes report-overlay-animation-remove{0%{opacity:1}100%{opacity:0}}[id^=NotiflixReportWrap].nx-remove>div[class*="-content"].nx-with-animation.nx-fade{opacity:0;-webkit-animation:report-animation-fade-remove .3s ease-in-out 0s normal;animation:report-animation-fade-remove .3s ease-in-out 0s normal}@-webkit-keyframes report-animation-fade-remove{0%{opacity:1}100%{opacity:0}}@keyframes report-animation-fade-remove{0%{opacity:1}100%{opacity:0}}[id^=NotiflixReportWrap].nx-remove>div[class*="-content"].nx-with-animation.nx-zoom{opacity:0;-webkit-animation:report-animation-zoom-remove .3s ease-in-out 0s normal;animation:report-animation-zoom-remove .3s ease-in-out 0s normal}@-webkit-keyframes report-animation-zoom-remove{0%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:.5;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:0;-webkit-transform:scale(0);transform:scale(0)}}@keyframes report-animation-zoom-remove{0%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:.5;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:0;-webkit-transform:scale(0);transform:scale(0)}}'},ft=function(t,a,n,m,p,k){if(!tt("body"))return!1;o||X.Report.init({});var u={};if(typeof p=="object"&&!Array.isArray(p)||typeof k=="object"&&!Array.isArray(k)){var l={};typeof p=="object"?l=p:typeof k=="object"&&(l=k),u=z(!0,o,{}),o=z(!0,o,l)}var w=o[t.toLocaleLowerCase("en")];typeof a!="string"&&(a="Notiflix "+t),typeof n!="string"&&(t===S.Success?n='"Do not try to become a person of success but try to become a person of value." <br><br>- Albert Einstein':t===S.Failure?n='"Failure is simply the opportunity to begin again, this time more intelligently." <br><br>- Henry Ford':t===S.Warning?n='"The peoples who want to live comfortably without producing and fatigue; they are doomed to lose their dignity, then liberty, and then independence and destiny." <br><br>- Mustafa Kemal Ataturk':t===S.Info&&(n='"Knowledge rests not upon truth alone, but upon error also." <br><br>- Carl Gustav Jung')),typeof m!="string"&&(m="Okay"),o.plainText&&(a=_(a),n=_(n),m=_(m)),o.plainText||(a.length>o.titleMaxLength&&(a="Possible HTML Tags Error",n='The "plainText" option is "false" and the title content length is more than the "titleMaxLength" option.',m="Okay"),n.length>o.messageMaxLength&&(a="Possible HTML Tags Error",n='The "plainText" option is "false" and the message content length is more than the "messageMaxLength" option.',m="Okay"),m.length>o.buttonMaxLength&&(a="Possible HTML Tags Error",n='The "plainText" option is "false" and the button content length is more than the "buttonMaxLength" option.',m="Okay")),a.length>o.titleMaxLength&&(a=a.substring(0,o.titleMaxLength)+"..."),n.length>o.messageMaxLength&&(n=n.substring(0,o.messageMaxLength)+"..."),m.length>o.buttonMaxLength&&(m=m.substring(0,o.buttonMaxLength)+"..."),o.cssAnimation||(o.cssAnimationDuration=0);var f=e.document.createElement("div");f.id=J.ID,f.className=o.className,f.style.zIndex=o.zindex,f.style.borderRadius=o.borderRadius,f.style.fontFamily='"'+o.fontFamily+'", '+b,o.rtl&&(f.setAttribute("dir","rtl"),f.classList.add("nx-rtl-on")),f.style.display="flex",f.style.flexWrap="wrap",f.style.flexDirection="column",f.style.alignItems="center",f.style.justifyContent="center";var L="",E=o.backOverlayClickToClose===!0;o.backOverlay&&(L='<div class="'+o.className+"-overlay"+(o.cssAnimation?" nx-with-animation":"")+(E?" nx-report-click-to-close":"")+'" style="background:'+(w.backOverlayColor||o.backOverlayColor)+";animation-duration:"+o.cssAnimationDuration+'ms;"></div>');var I="";if(t===S.Success?I=qt(o.svgSize,w.svgColor):t===S.Failure?I=Kt(o.svgSize,w.svgColor):t===S.Warning?I=Jt(o.svgSize,w.svgColor):t===S.Info&&(I=Gt(o.svgSize,w.svgColor)),f.innerHTML=L+'<div class="'+o.className+"-content"+(o.cssAnimation?" nx-with-animation ":"")+" nx-"+o.cssAnimationStyle+'" style="width:'+o.width+"; background:"+o.backgroundColor+"; animation-duration:"+o.cssAnimationDuration+'ms;"><div style="width:'+o.svgSize+"; height:"+o.svgSize+';" class="'+o.className+'-icon">'+I+'</div><h5 class="'+o.className+'-title" style="font-weight:500; font-size:'+o.titleFontSize+"; color:"+w.titleColor+';">'+a+'</h5><p class="'+o.className+'-message" style="font-size:'+o.messageFontSize+"; color:"+w.messageColor+';">'+n+'</p><a id="NXReportButton" class="'+o.className+'-button" style="font-weight:500; font-size:'+o.buttonFontSize+"; background:"+w.buttonBackground+"; color:"+w.buttonColor+';">'+m+"</a></div>",!e.document.getElementById(f.id)){e.document.body.appendChild(f);var A=function(){var C=e.document.getElementById(f.id);C.classList.add("nx-remove");var $=setTimeout(function(){C.parentNode!==null&&C.parentNode.removeChild(C),clearTimeout($)},o.cssAnimationDuration)},v=e.document.getElementById("NXReportButton");if(v.addEventListener("click",function(){typeof p=="function"&&p(),A()}),L&&E){var W=e.document.querySelector(".nx-report-click-to-close");W.addEventListener("click",function(){A()})}}o=z(!0,o,u)},ee=function(){return'[id^=NotiflixConfirmWrap]{position:fixed;z-index:4003;width:100%;height:100%;left:0;top:0;padding:10px;-webkit-box-sizing:border-box;box-sizing:border-box;background:transparent;font-family:"Quicksand",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}[id^=NotiflixConfirmWrap].nx-position-center-top{-webkit-box-pack:start;-webkit-justify-content:flex-start;-ms-flex-pack:start;justify-content:flex-start}[id^=NotiflixConfirmWrap].nx-position-center-bottom{-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end}[id^=NotiflixConfirmWrap].nx-position-left-top{-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;-webkit-box-pack:start;-webkit-justify-content:flex-start;-ms-flex-pack:start;justify-content:flex-start}[id^=NotiflixConfirmWrap].nx-position-left-center{-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start}[id^=NotiflixConfirmWrap].nx-position-left-bottom{-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end}[id^=NotiflixConfirmWrap].nx-position-right-top{-webkit-box-align:end;-webkit-align-items:flex-end;-ms-flex-align:end;align-items:flex-end;-webkit-box-pack:start;-webkit-justify-content:flex-start;-ms-flex-pack:start;justify-content:flex-start}[id^=NotiflixConfirmWrap].nx-position-right-center{-webkit-box-align:end;-webkit-align-items:flex-end;-ms-flex-align:end;align-items:flex-end}[id^=NotiflixConfirmWrap].nx-position-right-bottom{-webkit-box-align:end;-webkit-align-items:flex-end;-ms-flex-align:end;align-items:flex-end;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end}[id^=NotiflixConfirmWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixConfirmWrap]>div[class*="-overlay"]{width:100%;height:100%;left:0;top:0;background:rgba(255,255,255,.5);position:fixed;z-index:0}[id^=NotiflixConfirmWrap]>div[class*="-overlay"].nx-with-animation{-webkit-animation:confirm-overlay-animation .3s ease-in-out 0s normal;animation:confirm-overlay-animation .3s ease-in-out 0s normal}@-webkit-keyframes confirm-overlay-animation{0%{opacity:0}100%{opacity:1}}@keyframes confirm-overlay-animation{0%{opacity:0}100%{opacity:1}}[id^=NotiflixConfirmWrap].nx-remove>div[class*="-overlay"].nx-with-animation{opacity:0;-webkit-animation:confirm-overlay-animation-remove .3s ease-in-out 0s normal;animation:confirm-overlay-animation-remove .3s ease-in-out 0s normal}@-webkit-keyframes confirm-overlay-animation-remove{0%{opacity:1}100%{opacity:0}}@keyframes confirm-overlay-animation-remove{0%{opacity:1}100%{opacity:0}}[id^=NotiflixConfirmWrap]>div[class*="-content"]{width:300px;max-width:100%;max-height:96vh;overflow-x:hidden;overflow-y:auto;border-radius:25px;padding:10px;margin:0;-webkit-filter:drop-shadow(0 0 5px rgba(0,0,0,0.05));filter:drop-shadow(0 0 5px rgba(0, 0, 0, .05));background:#f8f8f8;color:#1e1e1e;position:relative;z-index:1;text-align:center}[id^=NotiflixConfirmWrap]>div[class*="-content"]::-webkit-scrollbar{width:0;height:0}[id^=NotiflixConfirmWrap]>div[class*="-content"]::-webkit-scrollbar-thumb{background:transparent}[id^=NotiflixConfirmWrap]>div[class*="-content"]::-webkit-scrollbar-track{background:transparent}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]{float:left;width:100%;text-align:inherit}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>h5{float:left;width:100%;margin:0;padding:0 0 10px;border-bottom:1px solid rgba(0,0,0,.1);color:#32c682;font-family:inherit!important;font-size:16px;line-height:1.4;font-weight:500;text-align:inherit}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div{font-family:inherit!important;margin:15px 0 20px;padding:0 10px;float:left;width:100%;font-size:14px;line-height:1.4;font-weight:normal;color:inherit;text-align:inherit}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div>div{font-family:inherit!important;float:left;width:100%;margin:15px 0 0;padding:0}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div>div>input{font-family:inherit!important;float:left;width:100%;height:40px;margin:0;padding:0 15px;border:1px solid rgba(0,0,0,.1);border-radius:25px;font-size:14px;font-weight:normal;line-height:1;-webkit-transition:all .25s ease-in-out;-o-transition:all .25s ease-in-out;transition:all .25s ease-in-out;text-align:left}[id^=NotiflixConfirmWrap].nx-rtl-on>div[class*="-content"]>div[class*="-head"]>div>div>input{text-align:right}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div>div>input:hover{border-color:rgba(0,0,0,.1)}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div>div>input:focus{border-color:rgba(0,0,0,.3)}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div>div>input.nx-validation-failure{border-color:#ff5549}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-head"]>div>div>input.nx-validation-success{border-color:#32c682}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-buttons"]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border-radius:inherit;float:left;width:100%;text-align:inherit}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-buttons"]>a{cursor:pointer;font-family:inherit!important;-webkit-transition:all .25s ease-in-out;-o-transition:all .25s ease-in-out;transition:all .25s ease-in-out;float:left;width:48%;padding:9px 5px;border-radius:inherit!important;font-weight:500;font-size:15px;line-height:1.4;color:#f8f8f8;text-align:inherit}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-buttons"]>a.nx-confirm-button-ok{margin:0 2% 0 0;background:#32c682}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-buttons"]>a.nx-confirm-button-cancel{margin:0 0 0 2%;background:#a9a9a9}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-buttons"]>a.nx-full{margin:0;width:100%}[id^=NotiflixConfirmWrap]>div[class*="-content"]>div[class*="-buttons"]>a:hover{-webkit-box-shadow:inset 0 -60px 5px -5px rgba(0,0,0,.25);box-shadow:inset 0 -60px 5px -5px rgba(0,0,0,.25)}[id^=NotiflixConfirmWrap].nx-rtl-on>div[class*="-content"]>div[class*="-buttons"],[id^=NotiflixConfirmWrap].nx-rtl-on>div[class*="-content"]>div[class*="-buttons"]>a{-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}[id^=NotiflixConfirmWrap].nx-with-animation.nx-fade>div[class*="-content"]{-webkit-animation:confirm-animation-fade .3s ease-in-out 0s normal;animation:confirm-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes confirm-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes confirm-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixConfirmWrap].nx-with-animation.nx-zoom>div[class*="-content"]{-webkit-animation:confirm-animation-zoom .3s ease-in-out 0s normal;animation:confirm-animation-zoom .3s ease-in-out 0s normal}@-webkit-keyframes confirm-animation-zoom{0%{opacity:0;-webkit-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}@keyframes confirm-animation-zoom{0%{opacity:0;-webkit-transform:scale(.5);transform:scale(.5)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}}[id^=NotiflixConfirmWrap].nx-with-animation.nx-fade.nx-remove>div[class*="-content"]{opacity:0;-webkit-animation:confirm-animation-fade-remove .3s ease-in-out 0s normal;animation:confirm-animation-fade-remove .3s ease-in-out 0s normal}@-webkit-keyframes confirm-animation-fade-remove{0%{opacity:1}100%{opacity:0}}@keyframes confirm-animation-fade-remove{0%{opacity:1}100%{opacity:0}}[id^=NotiflixConfirmWrap].nx-with-animation.nx-zoom.nx-remove>div[class*="-content"]{opacity:0;-webkit-animation:confirm-animation-zoom-remove .3s ease-in-out 0s normal;animation:confirm-animation-zoom-remove .3s ease-in-out 0s normal}@-webkit-keyframes confirm-animation-zoom-remove{0%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:.5;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:0;-webkit-transform:scale(0);transform:scale(0)}}@keyframes confirm-animation-zoom-remove{0%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:.5;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{opacity:0;-webkit-transform:scale(0);transform:scale(0)}}'},gt=function(t,a,n,m,p,k,u,l,w){if(!tt("body"))return!1;r||X.Confirm.init({});var f=z(!0,r,{});typeof w!="object"||Array.isArray(w)||(r=z(!0,r,w)),typeof a!="string"&&(a="Notiflix Confirm"),typeof n!="string"&&(n="Do you agree with me?"),typeof p!="string"&&(p="Yes"),typeof k!="string"&&(k="No"),typeof u!="function"&&(u=void 0),typeof l!="function"&&(l=void 0),r.plainText&&(a=_(a),n=_(n),p=_(p),k=_(k)),r.plainText||(a.length>r.titleMaxLength&&(a="Possible HTML Tags Error",n='The "plainText" option is "false" and the title content length is more than "titleMaxLength" option.',p="Okay",k="..."),n.length>r.messageMaxLength&&(a="Possible HTML Tags Error",n='The "plainText" option is "false" and the message content length is more than "messageMaxLength" option.',p="Okay",k="..."),(p.length||k.length)>r.buttonsMaxLength&&(a="Possible HTML Tags Error",n='The "plainText" option is "false" and the buttons content length is more than "buttonsMaxLength" option.',p="Okay",k="...")),a.length>r.titleMaxLength&&(a=a.substring(0,r.titleMaxLength)+"..."),n.length>r.messageMaxLength&&(n=n.substring(0,r.messageMaxLength)+"..."),p.length>r.buttonsMaxLength&&(p=p.substring(0,r.buttonsMaxLength)+"..."),k.length>r.buttonsMaxLength&&(k=k.substring(0,r.buttonsMaxLength)+"..."),r.cssAnimation||(r.cssAnimationDuration=0);var L=e.document.createElement("div");L.id=y.ID,L.className=r.className+(r.cssAnimation?" nx-with-animation nx-"+r.cssAnimationStyle:""),L.style.zIndex=r.zindex,L.style.padding=r.distance,r.rtl&&(L.setAttribute("dir","rtl"),L.classList.add("nx-rtl-on"));var E=typeof r.position=="string"?r.position.trim():"center";L.classList.add("nx-position-"+E),L.style.fontFamily='"'+r.fontFamily+'", '+b;var I="";r.backOverlay&&(I='<div class="'+r.className+"-overlay"+(r.cssAnimation?" nx-with-animation":"")+'" style="background:'+r.backOverlayColor+";animation-duration:"+r.cssAnimationDuration+'ms;"></div>');var A="";typeof u=="function"&&(A='<a id="NXConfirmButtonCancel" class="nx-confirm-button-cancel" style="color:'+r.cancelButtonColor+";background:"+r.cancelButtonBackground+";font-size:"+r.buttonsFontSize+';">'+k+"</a>");var v="",W=null,C=void 0;if(t===P.Ask||t===P.Prompt){W=m||"";var $=t===P.Ask||200<W.length?Math.ceil(1.5*W.length):250,Q=t===P.Prompt?'value="'+W+'"':"";v='<div><input id="NXConfirmValidationInput" type="text" '+Q+' maxlength="'+$+'" style="font-size:'+r.messageFontSize+";border-radius: "+r.borderRadius+';" autocomplete="off" spellcheck="false" autocapitalize="none" /></div>'}if(L.innerHTML=I+'<div class="'+r.className+'-content" style="width:'+r.width+"; background:"+r.backgroundColor+"; animation-duration:"+r.cssAnimationDuration+"ms; border-radius: "+r.borderRadius+';"><div class="'+r.className+'-head"><h5 style="color:'+r.titleColor+";font-size:"+r.titleFontSize+';">'+a+'</h5><div style="color:'+r.messageColor+";font-size:"+r.messageFontSize+';">'+n+v+'</div></div><div class="'+r.className+'-buttons"><a id="NXConfirmButtonOk" class="nx-confirm-button-ok'+(typeof u=="function"?"":" nx-full")+'" style="color:'+r.okButtonColor+";background:"+r.okButtonBackground+";font-size:"+r.buttonsFontSize+';">'+p+"</a>"+A+"</div></div>",!e.document.getElementById(L.id)){e.document.body.appendChild(L);var F=e.document.getElementById(L.id),U=e.document.getElementById("NXConfirmButtonOk"),M=e.document.getElementById("NXConfirmValidationInput");if(M&&(M.focus(),M.setSelectionRange(0,(M.value||"").length),M.addEventListener("keyup",function(g){var et=g.target.value;if(t===P.Ask&&et!==W)g.preventDefault(),M.classList.add("nx-validation-failure"),M.classList.remove("nx-validation-success");else{t===P.Ask&&(M.classList.remove("nx-validation-failure"),M.classList.add("nx-validation-success"));var ot=(g.key||"").toLocaleLowerCase("en")==="enter"||g.keyCode===13;ot&&U.dispatchEvent(new Event("click"))}})),U.addEventListener("click",function(g){if(t===P.Ask&&W&&M){var et=(M.value||"").toString();if(et!==W)return M.focus(),M.classList.add("nx-validation-failure"),g.stopPropagation(),g.preventDefault(),g.returnValue=!1,g.cancelBubble=!0,!1;M.classList.remove("nx-validation-failure")}typeof u=="function"&&(t===P.Prompt&&M&&(C=M.value||""),u(C)),F.classList.add("nx-remove");var ot=setTimeout(function(){F.parentNode!==null&&(F.parentNode.removeChild(F),clearTimeout(ot))},r.cssAnimationDuration)}),typeof u=="function"){var K=e.document.getElementById("NXConfirmButtonCancel");K.addEventListener("click",function(){typeof l=="function"&&(t===P.Prompt&&M&&(C=M.value||""),l(C)),F.classList.add("nx-remove");var g=setTimeout(function(){F.parentNode!==null&&(F.parentNode.removeChild(F),clearTimeout(g))},r.cssAnimationDuration)})}}r=z(!0,r,f)},ie=function(){return'[id^=NotiflixLoadingWrap]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:fixed;z-index:4000;width:100%;height:100%;left:0;top:0;right:0;bottom:0;margin:auto;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;background:rgba(0,0,0,.8);font-family:"Quicksand",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif}[id^=NotiflixLoadingWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixLoadingWrap].nx-loading-click-to-close{cursor:pointer}[id^=NotiflixLoadingWrap]>div[class*="-icon"]{width:60px;height:60px;position:relative;-webkit-transition:top .2s ease-in-out;-o-transition:top .2s ease-in-out;transition:top .2s ease-in-out;margin:0 auto}[id^=NotiflixLoadingWrap]>div[class*="-icon"] img,[id^=NotiflixLoadingWrap]>div[class*="-icon"] svg{max-width:unset;max-height:unset;width:100%;height:auto;position:absolute;left:0;top:0}[id^=NotiflixLoadingWrap]>p{position:relative;margin:10px auto 0;font-family:inherit!important;font-weight:normal;font-size:15px;line-height:1.4;padding:0 10px;width:100%;text-align:center}[id^=NotiflixLoadingWrap].nx-with-animation{-webkit-animation:loading-animation-fade .3s ease-in-out 0s normal;animation:loading-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes loading-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes loading-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixLoadingWrap].nx-with-animation.nx-remove{opacity:0;-webkit-animation:loading-animation-fade-remove .3s ease-in-out 0s normal;animation:loading-animation-fade-remove .3s ease-in-out 0s normal}@-webkit-keyframes loading-animation-fade-remove{0%{opacity:1}100%{opacity:0}}@keyframes loading-animation-fade-remove{0%{opacity:1}100%{opacity:0}}[id^=NotiflixLoadingWrap]>p.nx-loading-message-new{-webkit-animation:loading-new-message-fade .3s ease-in-out 0s normal;animation:loading-new-message-fade .3s ease-in-out 0s normal}@-webkit-keyframes loading-new-message-fade{0%{opacity:0}100%{opacity:1}}@keyframes loading-new-message-fade{0%{opacity:0}100%{opacity:1}}'},q=function(t,a,n,m,p){if(!tt("body"))return!1;s||X.Loading.init({});var k=z(!0,s,{});if(typeof a=="object"&&!Array.isArray(a)||typeof n=="object"&&!Array.isArray(n)){var u={};typeof a=="object"?u=a:typeof n=="object"&&(u=n),s=z(!0,s,u)}var l="";if(typeof a=="string"&&0<a.length&&(l=a),m){l=l.length>s.messageMaxLength?_(l).toString().substring(0,s.messageMaxLength)+"...":_(l).toString();var w="";0<l.length&&(w='<p id="'+s.messageID+'" class="nx-loading-message" style="color:'+s.messageColor+";font-size:"+s.messageFontSize+';">'+l+"</p>"),s.cssAnimation||(s.cssAnimationDuration=0);var f="";if(t===R.Standard)f=St(s.svgSize,s.svgColor);else if(t===R.Hourglass)f=Rt(s.svgSize,s.svgColor);else if(t===R.Circle)f=Mt(s.svgSize,s.svgColor);else if(t===R.Arrows)f=At(s.svgSize,s.svgColor);else if(t===R.Dots)f=Et(s.svgSize,s.svgColor);else if(t===R.Pulse)f=Bt(s.svgSize,s.svgColor);else if(t===R.Custom&&s.customSvgCode!==null&&s.customSvgUrl===null)f=s.customSvgCode||"";else if(t===R.Custom&&s.customSvgUrl!==null&&s.customSvgCode===null)f='<img class="nx-custom-loading-icon" width="'+s.svgSize+'" height="'+s.svgSize+'" src="'+s.customSvgUrl+'" alt="Notiflix">';else{if(t===R.Custom&&(s.customSvgUrl===null||s.customSvgCode===null))return j('You have to set a static SVG url to "customSvgUrl" option to use Loading Custom.'),!1;f=Yt(s.svgSize,"#f8f8f8","#32c682")}var L=parseInt((s.svgSize||"").replace(/[^0-9]/g,"")),E=e.innerWidth,I=L>=E?E-40+"px":L+"px",A='<div style="width:'+I+"; height:"+I+';" class="'+s.className+"-icon"+(0<l.length?" nx-with-message":"")+'">'+f+"</div>",v=e.document.createElement("div");if(v.id=H.ID,v.className=s.className+(s.cssAnimation?" nx-with-animation":"")+(s.clickToClose?" nx-loading-click-to-close":""),v.style.zIndex=s.zindex,v.style.background=s.backgroundColor,v.style.animationDuration=s.cssAnimationDuration+"ms",v.style.fontFamily='"'+s.fontFamily+'", '+b,v.style.display="flex",v.style.flexWrap="wrap",v.style.flexDirection="column",v.style.alignItems="center",v.style.justifyContent="center",s.rtl&&(v.setAttribute("dir","rtl"),v.classList.add("nx-rtl-on")),v.innerHTML=A+w,!e.document.getElementById(v.id)&&(e.document.body.appendChild(v),s.clickToClose)){var W=e.document.getElementById(v.id);W.addEventListener("click",function(){v.classList.add("nx-remove");var Q=setTimeout(function(){v.parentNode!==null&&(v.parentNode.removeChild(v),clearTimeout(Q))},s.cssAnimationDuration)})}}else if(e.document.getElementById(H.ID))var C=e.document.getElementById(H.ID),$=setTimeout(function(){C.classList.add("nx-remove");var Q=setTimeout(function(){C.parentNode!==null&&(C.parentNode.removeChild(C),clearTimeout(Q))},s.cssAnimationDuration);clearTimeout($)},p);s=z(!0,s,k)},ae=function(t){typeof t!="string"&&(t="");var a=e.document.getElementById(H.ID);if(a)if(0<t.length){t=t.length>s.messageMaxLength?_(t).substring(0,s.messageMaxLength)+"...":_(t);var n=a.getElementsByTagName("p")[0];if(n)n.innerHTML=t;else{var m=e.document.createElement("p");m.id=s.messageID,m.className="nx-loading-message nx-loading-message-new",m.style.color=s.messageColor,m.style.fontSize=s.messageFontSize,m.innerHTML=t,a.appendChild(m)}}else j("Where is the new message?")},ne=function(){return'[id^=NotiflixBlockWrap]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1000;font-family:"Quicksand",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;background:rgba(255,255,255,.9);text-align:center;animation-duration:.4s;width:100%;height:100%;left:0;top:0;border-radius:inherit;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}[id^=NotiflixBlockWrap] *{-webkit-box-sizing:border-box;box-sizing:border-box}[id^=NotiflixBlockWrap]>span[class*="-icon"]{display:block;width:45px;height:45px;position:relative;margin:0 auto}[id^=NotiflixBlockWrap]>span[class*="-icon"] svg{width:inherit;height:inherit}[id^=NotiflixBlockWrap]>span[class*="-message"]{position:relative;display:block;width:100%;margin:10px auto 0;padding:0 10px;font-family:inherit!important;font-weight:normal;font-size:14px;line-height:1.4}[id^=NotiflixBlockWrap].nx-with-animation{-webkit-animation:block-animation-fade .3s ease-in-out 0s normal;animation:block-animation-fade .3s ease-in-out 0s normal}@-webkit-keyframes block-animation-fade{0%{opacity:0}100%{opacity:1}}@keyframes block-animation-fade{0%{opacity:0}100%{opacity:1}}[id^=NotiflixBlockWrap].nx-with-animation.nx-remove{opacity:0;-webkit-animation:block-animation-fade-remove .3s ease-in-out 0s normal;animation:block-animation-fade-remove .3s ease-in-out 0s normal}@-webkit-keyframes block-animation-fade-remove{0%{opacity:1}100%{opacity:0}}@keyframes block-animation-fade-remove{0%{opacity:1}100%{opacity:0}}'},yt=0,G=function(t,a,n,m,p,k){var u;if(Array.isArray(n)){if(1>n.length)return j("Array of HTMLElements should contains at least one HTMLElement."),!1;u=n}else if(Object.prototype.isPrototypeOf.call(NodeList.prototype,n)){if(1>n.length)return j("NodeListOf<HTMLElement> should contains at least one HTMLElement."),!1;u=Array.prototype.slice.call(n)}else{var l=typeof n!="string"||1>(n||"").length||(n||"").length===1&&((n||"")[0]==="#"||(n||"")[0]===".");if(l)return j("The selector parameter must be a string and matches a specified CSS selector(s)."),!1;var w=e.document.querySelectorAll(n);if(1>w.length)return j('You called the "Notiflix.Block..." function with "'+n+'" selector, but there is no such element(s) in the document.'),!1;u=w}c||X.Block.init({});var f=z(!0,c,{});if(typeof m=="object"&&!Array.isArray(m)||typeof p=="object"&&!Array.isArray(p)){var L={};typeof m=="object"?L=m:typeof p=="object"&&(L=p),c=z(!0,c,L)}var E="";typeof m=="string"&&0<m.length&&(E=m),c.cssAnimation||(c.cssAnimationDuration=0);var I=T.className;typeof c.className=="string"&&(I=c.className.trim());var A=typeof c.querySelectorLimit=="number"?c.querySelectorLimit:200,v=(u||[]).length>=A?A:u.length,W="nx-block-temporary-position";if(t){for(var C,$=["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr","html","head","title","script","style","iframe"],Q=0;Q<v;Q++)if(C=u[Q],C){if(-1<$.indexOf(C.tagName.toLocaleLowerCase("en")))break;var F=C.querySelectorAll("[id^="+T.ID+"]");if(1>F.length){var U="";a&&(a===O.Hourglass?U=Rt(c.svgSize,c.svgColor):a===O.Circle?U=Mt(c.svgSize,c.svgColor):a===O.Arrows?U=At(c.svgSize,c.svgColor):a===O.Dots?U=Et(c.svgSize,c.svgColor):a===O.Pulse?U=Bt(c.svgSize,c.svgColor):U=St(c.svgSize,c.svgColor));var M='<span class="'+I+'-icon" style="width:'+c.svgSize+";height:"+c.svgSize+';">'+U+"</span>",K="";0<E.length&&(E=E.length>c.messageMaxLength?_(E).substring(0,c.messageMaxLength)+"...":_(E),K='<span style="font-size:'+c.messageFontSize+";color:"+c.messageColor+';" class="'+I+'-message">'+E+"</span>"),yt++;var g=e.document.createElement("div");g.id=T.ID+"-"+yt,g.className=I+(c.cssAnimation?" nx-with-animation":""),g.style.position=c.position,g.style.zIndex=c.zindex,g.style.background=c.backgroundColor,g.style.animationDuration=c.cssAnimationDuration+"ms",g.style.fontFamily='"'+c.fontFamily+'", '+b,g.style.display="flex",g.style.flexWrap="wrap",g.style.flexDirection="column",g.style.alignItems="center",g.style.justifyContent="center",c.rtl&&(g.setAttribute("dir","rtl"),g.classList.add("nx-rtl-on")),g.innerHTML=M+K;var et=e.getComputedStyle(C).getPropertyValue("position"),ot=typeof et=="string"?et.toLocaleLowerCase("en"):"relative",Wt=Math.round(1.25*parseInt(c.svgSize))+40,oe=C.offsetHeight||0,kt="";Wt>oe&&(kt="min-height:"+Wt+"px;");var It="";It=C.getAttribute("id")?"#"+C.getAttribute("id"):C.classList[0]?"."+C.classList[0]:(C.tagName||"").toLocaleLowerCase("en");var Ot="",Tt=-1>=["absolute","relative","fixed","sticky"].indexOf(ot);if(Tt||0<kt.length){if(!tt("head"))return!1;Tt&&(Ot="position:relative!important;");var re='<style id="Style-'+T.ID+"-"+yt+'">'+It+"."+W+"{"+Ot+kt+"}</style>",Dt=e.document.createRange();Dt.selectNode(e.document.head);var se=Dt.createContextualFragment(re);e.document.head.appendChild(se),C.classList.add(W)}C.appendChild(g)}}}else var le=function(D){var V=setTimeout(function(){D.parentNode!==null&&D.parentNode.removeChild(D);var rt=D.getAttribute("id"),pt=e.document.getElementById("Style-"+rt);pt&&pt.parentNode!==null&&pt.parentNode.removeChild(pt),clearTimeout(V)},c.cssAnimationDuration)},ce=function(D){if(D&&0<D.length)for(var V,rt=0;rt<D.length;rt++)V=D[rt],V&&(V.classList.add("nx-remove"),le(V));else Lt(typeof n=="string"?'"Notiflix.Block.remove();" function called with "'+n+'" selector, but this selector does not have a "Block" element to remove.':'"Notiflix.Block.remove();" function called with "'+n+'", but this "Array<HTMLElement>" or "NodeListOf<HTMLElement>" does not have a "Block" element to remove.')},me=function(D){var V=setTimeout(function(){D.classList.remove(W),clearTimeout(V)},c.cssAnimationDuration+300)},fe=setTimeout(function(){for(var D,V=0;V<v;V++)D=u[V],D&&(me(D),F=D.querySelectorAll("[id^="+T.ID+"]"),ce(F));clearTimeout(fe)},k);c=z(!0,c,f)},X={Notify:{init:function(t){i=z(!0,d,t),nt(Zt,"NotiflixNotifyInternalCSS")},merge:function(t){return i?void(i=z(!0,i,t)):(j("You have to initialize the Notify module before call Merge function."),!1)},success:function(t,a,n){mt(N.Success,t,a,n)},failure:function(t,a,n){mt(N.Failure,t,a,n)},warning:function(t,a,n){mt(N.Warning,t,a,n)},info:function(t,a,n){mt(N.Info,t,a,n)}},Report:{init:function(t){o=z(!0,J,t),nt(te,"NotiflixReportInternalCSS")},merge:function(t){return o?void(o=z(!0,o,t)):(j("You have to initialize the Report module before call Merge function."),!1)},success:function(t,a,n,m,p){ft(S.Success,t,a,n,m,p)},failure:function(t,a,n,m,p){ft(S.Failure,t,a,n,m,p)},warning:function(t,a,n,m,p){ft(S.Warning,t,a,n,m,p)},info:function(t,a,n,m,p){ft(S.Info,t,a,n,m,p)}},Confirm:{init:function(t){r=z(!0,y,t),nt(ee,"NotiflixConfirmInternalCSS")},merge:function(t){return r?void(r=z(!0,r,t)):(j("You have to initialize the Confirm module before call Merge function."),!1)},show:function(t,a,n,m,p,k,u){gt(P.Show,t,a,null,n,m,p,k,u)},ask:function(t,a,n,m,p,k,u,l){gt(P.Ask,t,a,n,m,p,k,u,l)},prompt:function(t,a,n,m,p,k,u,l){gt(P.Prompt,t,a,n,m,p,k,u,l)}},Loading:{init:function(t){s=z(!0,H,t),nt(ie,"NotiflixLoadingInternalCSS")},merge:function(t){return s?void(s=z(!0,s,t)):(j("You have to initialize the Loading module before call Merge function."),!1)},standard:function(t,a){q(R.Standard,t,a,!0,0)},hourglass:function(t,a){q(R.Hourglass,t,a,!0,0)},circle:function(t,a){q(R.Circle,t,a,!0,0)},arrows:function(t,a){q(R.Arrows,t,a,!0,0)},dots:function(t,a){q(R.Dots,t,a,!0,0)},pulse:function(t,a){q(R.Pulse,t,a,!0,0)},custom:function(t,a){q(R.Custom,t,a,!0,0)},notiflix:function(t,a){q(R.Notiflix,t,a,!0,0)},remove:function(t){typeof t!="number"&&(t=0),q(null,null,null,!1,t)},change:function(t){ae(t)}},Block:{init:function(t){c=z(!0,T,t),nt(ne,"NotiflixBlockInternalCSS")},merge:function(t){return c?void(c=z(!0,c,t)):(j('You have to initialize the "Notiflix.Block" module before call Merge function.'),!1)},standard:function(t,a,n){G(!0,O.Standard,t,a,n)},hourglass:function(t,a,n){G(!0,O.Hourglass,t,a,n)},circle:function(t,a,n){G(!0,O.Circle,t,a,n)},arrows:function(t,a,n){G(!0,O.Arrows,t,a,n)},dots:function(t,a,n){G(!0,O.Dots,t,a,n)},pulse:function(t,a,n){G(!0,O.Pulse,t,a,n)},remove:function(t,a){typeof a!="number"&&(a=0),G(!1,null,t,null,null,a)}}};return typeof e.Notiflix=="object"?z(!0,e.Notiflix,{Notify:X.Notify,Report:X.Report,Confirm:X.Confirm,Loading:X.Loading,Block:X.Block}):{Notify:X.Notify,Report:X.Report,Confirm:X.Confirm,Loading:X.Loading,Block:X.Block}})});var zt=wt(xt());var Vt=wt(xt());var B=wt(xt());function ht(){return ut({Type:"none"})}function ut(e){return new Promise(i=>{channel.registerCall("app.ontestproxy",(o,r)=>i(r===0)),legacyNativeCmder.call("app.testProxy",0,e.Type,e.http?e.http.Host+":"+e.http.Port:"","","","https://music.163.com/")})}function Ut(e){return new Promise(i=>{channel.call("app.setLocalConfig",()=>i(),["Proxy","",JSON.stringify(e)])})}function vt(){return new Promise(e=>{channel.call("app.getLocalConfig",i=>e(JSON.parse(i||"{}")),["Proxy",""])})}function Pt(e,i){return new Promise((o,r)=>{let s=new XMLHttpRequest;s.open("GET",e,!0),s.responseType="blob",i&&s.addEventListener("progress",i),s.onload=()=>{s.status===200?o(s.response):r(s.statusText)},s.onerror=()=>r(s.statusText),s.send()})}var ke=e=>{let i="";for(let[o,r]of Object.entries(e))r!==void 0&&r.toString().length>0&&(i+=`set ${o}=${r}&& `);return i.slice(0,-4)};function we(e){let i="";return e.version&&(i+=" -v"),e.port&&(i+=` -p ${e.port}`),e.address&&(i+=` -a ${e.address}`),e.proxyUrl&&(i+=` -u ${e.proxyUrl}`),e.forceHost&&(i+=` -f ${e.forceHost}`),e.matchOrder&&(i+=` -o ${e.matchOrder.join(" ")}`),e.token&&(i+=` -t ${e.token}`),e.endpoint&&(i+=` -e ${e.endpoint}`),e.strict&&(i+=" -s"),e.cnrelay&&(i+=` -c ${e.cnrelay}`),e.help&&(i+=" -h"),i}var he=async(e,i,o,r=!1,s)=>{s.port=`${i}:${i+1}`;let c=`cmd /c ${ke(o)} && ${await betterncm.app.getDataPath()}${e} ${we(s)}`;console.log("Launching UNM: ",c,{visible:r,binaryPath:e,port:i,env:o,args:s}),betterncm.app.exec(c,!1,r)};async function _t(e,i){let o=i.getConfig("selectedVersion",null);if(!o){throw B.default.Notify.failure("[Revived UnblockMusic] \u672A\u9009\u4E2D UnblockNeteaseMusic \u7248\u672C\uFF0C\u8BF7\u5148\u5B89\u88C5"),Error("Failed to start UnblockNeteaseMusic: No version selected");return}let r=`./RevivedUnblockInstaller/${o.filename}`;if(betterncm_native.fs.exists(r)){let s=i.getConfig("source-order",Z),c=i.getConfig("other-settings",Nt);he(r,e,{ENABLE_LOCAL_VIP:(c.find(x=>x.code==="ENABLE_LOCAL_VIP")?.enable,"false"),BLOCK_ADS:c.find(x=>x.code==="BLOCK_ADS")?.enable||!1,DISABLE_UPGRADE_CHECK:c.find(x=>x.code==="DISABLE_UPGRADE_CHECK")?.enable||!1,SEARCH_ALBUM:c.find(x=>x.code==="SEARCH_ALBUM").enable,LOG_LEVEL:c.find(x=>x.code==="LOG_LEVEL").enable?"debug":"info",ENABLE_FLAC:c.find(x=>x.code==="ENABLE_FLAC")?.enable||!1,FOLLOW_SOURCE_ORDER:c.find(x=>x.code==="FOLLOW_SOURCE_ORDER").enable,QQ_COOKIE:i.getConfig("qq-cookie",""),YOUTUBE_KEY:i.getConfig("youtube-key",""),MIGU_COOKIE:i.getConfig("migu-cookie",""),JOOX_COOKIE:i.getConfig("joox-cookie",""),NETEASE_COOKIE:i.getConfig("netease-cookie","")},i.getConfig("visible",!1),{proxyUrl:i.getConfig("upstream-proxy",""),matchOrder:s.filter(x=>x.enable).map(x=>x.code)})}else{if(o.installed)throw B.default.Notify.failure("[Revived UnblockMusic] \u672A\u627E\u5230 UnblockNeteaseMusic \u4E8C\u8FDB\u5236\u6587\u4EF6\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\u7248\u672C\u5E76\u5B89\u88C5"),Error("Failed to start UnblockNeteaseMusic: Binary not found");return B.default.Loading.remove(),new Promise(async(s,c)=>{(await vt()).Type!=="none"?(B.default.Loading.dots("\u672A\u627E\u5230 UnblockNeteaseMusic \u4E8C\u8FDB\u5236\u6587\u4EF6\uFF0C\u5373\u5C06\u91CD\u542F\u7F51\u6613\u4E91\u97F3\u4E50..."),await betterncm.utils.delay(1e3),await it({Type:"none"})):await ht()?B.default.Confirm.show("Revived UnblockMusic","\u4F60\u9009\u4E2D\u7684\u7248\u672C Unblock \u7248\u672C\u672A\u5B89\u88C5\uFF0C\u662F\u5426\u4E0B\u8F7D\u5E76\u5B89\u88C5\uFF1F","\u4E0B\u8F7D\u5E76\u5B89\u88C5","\u5FFD\u7565",async()=>{B.default.Loading.circle(`\u6B63\u5728\u4E0B\u8F7D UnblockCloudMusic ${o.tag} (0%)`,{messageMaxLength:99999});let N=await Pt(o.download_url,d=>{document.querySelector(".nx-loading-message").innerText=`\u6B63\u5728\u4E0B\u8F7D UnblockCloudMusic ${o.tag} (${(d.loaded/d.total*100).toFixed(2)}% ${(d.loaded/1024/1024).toFixed(2)}M/${(d.total/1024/1024).toFixed(2)}M)`});B.default.Loading.change(`\u6B63\u5728\u5B89\u88C5 UnblockCloudMusic ${o.tag}`),await betterncm.fs.writeFile(`./${r}`,N),await _t(e,i),s()}):(B.default.Notify.failure("[Revived UnblockMusic] \u672A\u8FDE\u63A5\u7F51\u7EDC"),c(Error("Failed to start UnblockNeteaseMusic: No network connection")))})}}async function it(e,i=e.Type==="http"){i&&await betterncm.app.writeConfig("cc.microblock.betterncm.ignore-certificate-errors","true");let o=await vt();if(o.Type!=e.Type||o?.http?.Host!=e?.http?.Host||o?.http?.Port!=e?.http?.Port){e.http&&typeof e.http.Port=="number"&&(e.http.Port=e.http.Port.toString()),await Ut(e),await betterncm.utils.delay(300);async function r(){await betterncm.app.exec('cmd /c ping 127.0.0.1 -n 2 && start """""""" """"%cd%\\cloudmusic.exe""""',!1,!1),channel.call("app.exit",()=>{},["restart"])}await r()}}async function Y(e){e.setConfig("enabled",!0),await e.write();let i=e.getConfig("port",Math.round(Math.random()*1e4+1e4));B.default.Loading.remove(),B.default.Loading.circle("\u6B63\u5728\u68C0\u67E5 UnblockMusic \u670D\u52A1\u662F\u5426\u5DF2\u542F\u52A8...");let o={Type:"http",http:{Host:"localhost",Port:i}};if(await betterncm.utils.delay(100),await ut(o)){await it(o),B.default.Loading.remove(),B.default.Notify.success("UnblockMusic \u670D\u52A1\u5DF2\u542F\u52A8",{pauseOnHover:!1,position:"left-top",timeout:1e3});return}else{if(await betterncm.utils.delay(100),!await ht()){throw B.default.Loading.remove(),B.default.Notify.failure("[Revived UnblockMusic] \u672A\u8FDE\u63A5\u7F51\u7EDC"),Error("Failed to start UnblockNeteaseMusic: No network connection");return}B.default.Loading.change("\u6B63\u5728\u542F\u52A8 UnblockMusic \u670D\u52A1..."),await _t(i,e),await ut(o)?(B.default.Loading.change("\u6B63\u5728\u68C0\u67E5\u4EE3\u7406\u8BBE\u7F6E..."),await it(o),B.default.Loading.remove(),B.default.Notify.success("UnblockMusic \u670D\u52A1\u5DF2\u542F\u52A8",{pauseOnHover:!1,position:"left-top",timeout:1e3})):(B.default.Loading.remove(),B.default.Confirm.show("Revived UnblockMusic","\u542F\u52A8 UnblockMusic \u670D\u52A1\u5931\u8D25\uFF0C\u662F\u5426\u7981\u7528\uFF1F","\u7981\u7528","\u53D6\u6D88",async()=>{await it({Type:"none"}),e.setConfig("enabled",!1),await e.write()},()=>{}))}}var st=async()=>await betterncm.app.exec('taskkill /f /fi """"IMAGENAME eq UnblockNeteaseMusic-*""""'),Nt=[{name:"\u5728\u5176\u4ED6\u97F3\u6E90\u641C\u7D22\u6B4C\u66F2\u65F6\u643A\u5E26\u4E13\u8F91\u540D\u79F0",code:"SEARCH_ALBUM",enable:!1},{name:"\u4E25\u683C\u6309\u7167\u914D\u7F6E\u97F3\u6E90\u7684\u987A\u5E8F\u8FDB\u884C\u67E5\u8BE2",code:"FOLLOW_SOURCE_ORDER",enable:!1},{name:"\u6253\u5F00\u8C03\u8BD5\u6A21\u5F0F\uFF08\u4EC5\u9650\u5F00\u53D1\u8005\uFF09",code:"LOG_LEVEL",enable:!1}],Z=[{name:"QQ \u97F3\u4E50",code:"qq",enable:!0,note:"\u9700\u8981\u51C6\u5907\u81EA\u5DF1\u7684 QQ_COOKIE\uFF08\u8BF7\u53C2\u9605\u4E0B\u65B9\u3008\u73AF\u5883\u53D8\u91CF\u3009\u5904\uFF09\u3002\u5FC5\u987B\u4F7F\u7528 QQ \u767B\u5F55\u3002"},{name:"\u9177\u72D7\u97F3\u4E50",code:"kugou",enable:!0},{name:"\u9177\u6211\u97F3\u4E50",code:"kuwo",enable:!0},{name:"\u54AA\u5495\u97F3\u4E50",code:"migu",enable:!0,note:"\u9700\u8981\u51C6\u5907\u81EA\u5DF1\u7684 MIGU_COOKIE\uFF08\u8BF7\u53C2\u9605\u4E0B\u65B9\u3008\u73AF\u5883\u53D8\u91CF\u3009\u5904\uFF09\u3002"},{name:"JOOX",code:"joox",enable:!1,note:"\u9700\u8981\u51C6\u5907\u81EA\u5DF1\u7684 JOOX_COOKIE\uFF08\u8BF7\u53C2\u9605\u4E0B\u65B9\u3008\u73AF\u5883\u53D8\u91CF\u3009\u5904\uFF09\u3002\u4F3C\u4E4E\u6709\u4E25\u683C\u5730\u533A\u9650\u5236\u3002"},{name:"YouTube\uFF08\u7EAF JS \u89E3\u6790\u65B9\u5F0F\uFF09",code:"youtube",enable:!1,note:"\u9700\u8981 Google \u8BA4\u5B9A\u7684\u975E\u4E2D\u56FD\u5927\u9646\u533A\u57DF IP \u5730\u5740\u3002"},{name:"yt-download",code:"ytdownload",enable:!1,note:"\u4F3C\u4E4E\u4E0D\u80FD\u4F7F\u7528\u3002"},{name:"YouTube\uFF08\u901A\u8FC7 youtube-dl\uFF09",code:"youtubedl",enable:!0,note:"\u9700\u8981\u81EA\u884C\u5B89\u88C5 youtube-dl\u3002"},{name:"YouTube\uFF08\u901A\u8FC7 yt-dlp\uFF09",code:"ytdlp",enable:!0,note:"\u9700\u8981\u81EA\u884C\u5B89\u88C5 yt-dlp\uFF08youtube-dl \u4ECD\u5728\u6D3B\u8DC3\u7EF4\u62A4\u7684 fork\uFF09\u3002"},{name:"B \u7AD9\u97F3\u4E50",code:"bilibili",enable:!0},{name:"\u7B2C\u4E09\u65B9\u7F51\u6613\u4E91 API",code:"pyncmd",enable:!1}];var Ht=({defaultValue:e,onChange:i,children:o})=>{let[r,s]=React.useState(e);return h("div",{className:"rr-btn_container"},h("button",{className:`rr-btn_button rr-btn_${r?"enabled":"disabled"}`,onClick:()=>{s(!r),i&&i(!r)}},r?"\u5DF2\u542F\u7528":"\u5DF2\u7981\u7528"),r&&o)};var ve=({version:e,isSelected:i,onSelect:o})=>{let{tag:r,installed:s,filename:c,releaseDate:x,onlineFileName:b}=e;return h("div",{className:`version-item ${s?"installed":""} ${i?"selected":""}`,onClick:o},h("div",{className:"circle"},h("div",{className:"circle-inner"})),h("div",{className:"info"},h("div",{className:"tag"},r),h("div",{className:"details"},h("span",{className:"filename"},b??c),h("span",{className:"release-date"},x))))},jt=({UNMVersions:e,config:i,selectedVersionIndex:o})=>{let r=i,[s,c]=React.useState(o);React.useEffect(()=>{c(o)},[e]);let x=b=>{r.setConfig("selectedVersion",e[b]),r.write(),c(b)};return h("div",{className:"version-selector"},e.map((b,N)=>h(ve,{key:N,version:b,isSelected:s===N,onSelect:()=>x(N)})))};var at=({label:e,placeholder:i,onChange:o,defaultValue:r=""})=>h("div",{className:"input-container"},h("span",{className:"input-label"},e),h("input",{className:"input-box",placeholder:i,onChange:o,defaultValue:r}));function Ne({children:e,enable:i,onClick:o,text:r}){return h("div",{className:`badge ${i?"badge-enabled":"badge-disabled"}`,onClick:o,draggable:"true"},h("div",{className:"badge-text"},e??r))}function Ct({badges:e,onBadgeClick:i,onBadgeSwap:o,swapable:r=!1}){let s=(b,N)=>{let d=b.getBoundingClientRect(),S=N.getBoundingClientRect();return b.animate([{transform:"translate(0,0)"},{transform:`translate(${S.left-d.left}px, ${S.top-d.top}px)`}],{duration:200,easing:"ease-in-out"}),N.animate([{transform:"translate(0,0)"},{transform:`translate(${d.left-S.left}px, ${d.top-S.top}px)`}],{duration:200,easing:"ease-in-out"}).finished},c=React.useRef(null),x=(b,N)=>{let d=c.current,S=d.children[b],J=d.children[N];S&&J&&s(S,J).then(()=>{o(b,N)})};return h("div",{className:`badge-list ${r&&"badge-list-swap"}`,ref:c},e.map((b,N)=>h("div",{key:b.name,className:"badge-wrapper",draggable:"true"},h(Ne,{enable:b.enable,onClick:d=>{i(N)}},r&&h("button",{className:"badge-swap-button",onClick:d=>{d.stopPropagation(),x(N,N-1)}},"<"),h("span",null,b.name),r&&h("button",{className:"badge-swap-button",onClick:d=>{d.stopPropagation(),x(N,N+1)}},">")))))}function Ce({config:e}){let i=e.getConfig("other-settings",Nt),[o,r]=React.useState(i);return h(Ct,{badges:o,onBadgeClick:x=>{let b=[...o];b[x].enable=!b[x].enable,r(b),e.setConfig("other-settings",b),e.write()},onBadgeSwap:(x,b)=>{r(N=>{let d=[...N];return[d[x],d[b]]=[d[b],d[x]],e.setConfig("other-settings",d),e.write(),d})}})}function ze({config:e}){let i=e.getConfig("source-order",Z),[o,r]=React.useState(i);return h(Ct,{badges:o,onBadgeClick:x=>{let b=[...o];b[x].enable=!b[x].enable,r(b),e.setConfig("source-order",b),e.write()},onBadgeSwap:(x,b)=>{r(N=>{let d=[...N];return[d[x],d[b]]=[d[b],d[x]],e.setConfig("source-order",d),e.write(),d})},swapable:!0})}function $t({config:e,stylesheet:i}){let[o,r]=React.useState(0),[s,c]=React.useState([]),[x,b]=React.useState([]);React.useEffect(()=>{(async()=>{let R=(await betterncm.fs.readDir("./RevivedUnblockInstaller/")).map(T=>T.split(/\/|\\/g).pop()).filter(T=>T.startsWith("UnblockNeteaseMusic-")&&T.endsWith(".exe")).map(T=>T.replace("UnblockNeteaseMusic-","").replace(".exe",""));c(R);let H=await fetch("https://api.github.com/repos/UnblockNeteaseMusic/server/releases/latest").then(T=>T.json()),O=H.assets.find(T=>T.name.includes("win-x64"));b([{tag:H.tag_name,installed:R.includes(H.tag_name),filename:`UnblockNeteaseMusic-${H.tag_name}.exe`,onlineFileName:O.name,releaseDate:O.created_at,download_url:O.browser_download_url},{tag:H.tag_name+"-ghproxy",installed:R.includes(H.tag_name+"-ghproxy"),filename:`UnblockNeteaseMusic-${H.tag_name}-ghproxy.exe`,onlineFileName:O.name,releaseDate:O.created_at,download_url:"https://mirror.ghproxy.com/"+O.browser_download_url}])})()},[]),React.useEffect(()=>{let y=()=>{r(R=>R+1)};return e.addEventListener("change",y),()=>e.removeEventListener("change",y)},[e]);let N=async()=>{let y=e.getConfig("visible",!1);e.setConfig("visible",!y),await e.write(),await st(),await Y(e)},d=async()=>{if(!e.getConfig("selectedVersion",null)){Vt.default.Notify.info("\u8BF7\u5148\u9009\u4E2D\u4E00\u4E2A\u7248\u672C");return}await st(),await Y(e)},S=React.useMemo(()=>x.concat(s.map(y=>({tag:y,installed:!0,filename:`UnblockNeteaseMusic-${y}.exe`,releaseDate:"\u672A\u77E5"}))),[x,s]),J=React.useMemo(()=>S.findIndex(y=>y.tag===e.getConfig("selectedVersion",null)?.tag),[S,o]),P=async y=>{e.setConfig("enabled",y),await e.write(),y?await Y(e):(await st(),await it({Type:"none"}))};return h("div",{className:"unm"},h("div",{className:"title"},"UnblockCloudMusic",h("div",{className:"revived"},"Revived")),h(Ht,{defaultValue:e.getConfig("enabled",!1),onChange:P},h("div",{className:"optionBlock versionSel"},h("div",{className:"optionTitle"},"\u4E0B\u8F7D"),h("div",{className:"optionSubtitle"},"\u7248\u672C\u9009\u62E9"),h(jt,{UNMVersions:S,selectedVersionIndex:J,config:e}),h("button",{className:"btn",onClick:()=>d()},"\u5E94\u7528")),h("div",{className:"optionBlock"},h("div",{className:"optionTitle"},"\u8FD0\u884C"),h("div",{className:"optionSubtitle"},"\u914D\u7F6E"),h(at,{label:"\u4E0A\u6E38\u4EE3\u7406",placeholder:"\u65E0\uFF08\u5982\uFF1Ahttp://127.0.0.1:7890/\uFF09",onChange:y=>{e.setConfig("upstream-proxy",y.target.value),e.write()},defaultValue:e.getConfig("upstream-proxy","")}),h("span",{className:"label"},"\u97F3\u6E90\u8BBE\u7F6E"),h("div",{style:{padding:"15px"}},h(ze,{config:e}),e.getConfig("source-order",Z).find(y=>y.code==="qq"&&y.enable)&&h(at,{label:"QQ\u97F3\u4E50 Cookies",placeholder:"QQ \u97F3\u6E90\u7684 uin \u548C qm_keyst Cookie\u3002\u5FC5\u987B\u4F7F\u7528 QQ \u767B\u5F55\u3002",onChange:y=>{e.setConfig("qq-cookie",y.target.value),e.write()},defaultValue:e.getConfig("qq-cookie","")}),e.getConfig("source-order",Z).find(y=>y.code==="joox"&&y.enable)&&h(at,{label:"JOOX Cookies",placeholder:"JOOX \u97F3\u6E90\u7684 wmid \u548C session_key Cookie\u3002",onChange:y=>{e.setConfig("joox-cookie",y.target.value),e.write()},defaultValue:e.getConfig("joox-cookie","")}),e.getConfig("source-order",Z).find(y=>y.code==="migu"&&y.enable)&&h(at,{label:"\u54AA\u5495\u97F3\u4E50 Cookies",placeholder:"\u54AA\u5495\u97F3\u6E90\u7684 aversionid Cookie\u3002",onChange:y=>{e.setConfig("migu-cookie",y.target.value),e.write()},defaultValue:e.getConfig("migu-cookie","")}),e.getConfig("source-order",Z).find(y=>y.code==="youtube"&&y.enable)&&h(at,{label:"Youtube Key",placeholder:"Youtube \u97F3\u6E90\u7684 Data API v3 Key\u3002",onChange:y=>{e.setConfig("youtube-key",y.target.value),e.write()},defaultValue:e.getConfig("youtube-key","")})),h("span",{className:"label"},"\u5176\u4ED6\u8BBE\u7F6E"),h("div",{style:{padding:"15px"}},h(Ce,{config:e})),h("div",{className:"note"},"\u6CE8\uFF1A\u4F60\u9700\u8981\u91CD\u542F\u8FDB\u7A0B\u540E\u914D\u7F6E\u624D\u80FD\u751F\u6548"),h("div",{className:"optionSubtitle"},"\u5F53\u524D\u7AEF\u53E3"),h("div",{style:{padding:"10px",fontSize:"20px"}},e.getConfig("port",Math.round(Math.random()*1e4+1e4))),h("div",{className:"optionSubtitle"},"\u64CD\u4F5C"),h("button",{style:{margin:"10px 5px"},className:"btn",onClick:()=>N()},"\u5207\u6362\u7A97\u53E3\u663E\u9690"),h("button",{style:{margin:"10px 5px"},className:"btn",onClick:async()=>{await st(),await Y(e)}},"\u91CD\u65B0\u542F\u52A8\u8FDB\u7A0B")),h("div",{className:"optionBlock"},h("div",{className:"optionTitle"},"\u5176\u4ED6"),h("div",{className:"optionSubtitle"},"\u5173\u4E8E"),h("div",{style:{padding:"10px",fontSize:"17px"}},"\u672C\u63D2\u4EF6\u8D44\u6E90\u4EC5\u4F9B\u5B66\u4E60\u4EA4\u6D41\uFF0C\u4E25\u7981\u7528\u4E8E\u5546\u4E1A\u7528\u9014\u3002",h("br",null),"\u6B4C\u66F2\u7248\u6743\u5F52\u539F\u4F5C\u8005\u6240\u6709\uFF0C\u5982\u6709\u4FB5\u6743\u8BF7\u8054\u7CFB\u5220\u9664\u3002",h("br",null),"\u672C\u63D2\u4EF6\u4EC5\u4E3A\u5B89\u88C5\u5668\uFF0C\u4E0D\u63D0\u4F9B\u4EFB\u4F55\u97F3\u4E50\u8D44\u6E90\u3002",h("br",null),"\u4E0D\u5EFA\u8BAE\u8FDB\u884C\u5927\u578B\u5BA3\u53D1\uFF0C\u4F20\u64AD\u3002"),h("div",{className:"optionSubtitle"},"\u70B9\u70B9 Star \u2B50"),h("button",{style:{margin:"10px 5px"},className:"btn",onClick:()=>betterncm.ncm.openUrl("https://github.com/UnblockNeteaseMusic/server")},"\u89E3\u7070\u6E90\u9879\u76EE"),h("button",{style:{margin:"10px 5px"},className:"btn",onClick:()=>betterncm.ncm.openUrl("https://github.com/ReviveUnblockNCMInstaller/RevivedUnblockInstaller")},"\u4E00\u952E\u5B89\u88C5\u5668\uFF08\u672C\u9879\u76EE\uFF09"))),h("style",null,i))}function Qt(){return h("div",null,h("h1",null,"UnblockInstaller \u4EC5\u652F\u6301 x64 \u7CFB\u7EDF"))}var bt=class extends EventTarget{constructor(o){super();this.path=o}async read(){try{this.data=JSON.parse(await betterncm.fs.readFileText(this.path))}catch{this.data={}}}readSync(){try{this.data=JSON.parse(betterncm_native.fs.readFileText(this.path))}catch{this.data={}}}async write(){await betterncm.fs.writeFileText(this.path,JSON.stringify(this.data))}getConfig(o,r){return this.data[o]===void 0&&(this.data[o]=r),this.data[o]}setConfig(o,r){this.data[o]=r,this.dispatchEvent(new CustomEvent("change"))}};var lt=new bt("./RevivedUnblockInstaller/config.json");plugin.onLoad(async function(){if(await betterncm.fs.mkdir("./RevivedUnblockInstaller"),await lt.read(),lt.getConfig("enabled",!1)){zt.default.Loading.circle("RevivedUnblockInstaller \u63D2\u4EF6\u6B63\u5728\u521D\u59CB\u5316...");try{Y(lt)}catch{zt.default.Loading.remove()}}await lt.write()});plugin.onConfig(function(){let e=document.createElement("div");return navigator.userAgent.includes("WOW64")?ReactDOM.render(h($t,{stylesheet:betterncm_native.fs.readFileText(this.pluginPath+"/style.css"),config:lt}),e):ReactDOM.render(h(Qt,null),e),e});})();
