// Generated from NCM 2.10.8

declare var defaultStatus:any;
declare var defaultstatus:any;
declare var webkitStorageInfo:any;
declare var channel:any;
declare var registeredCalls:any;
declare var CMRudio:any;
declare var xstream:any;
declare var withDevServer:any;
declare var puzzleEnv:any;
declare var puzzle:any;
declare var Rx:any;
declare var RxOperators:any;
declare var webpackJsonp:any;
declare var regeneratorRuntime:any;
declare var legacyNativeCmder:any;
declare var niIsSupportCapability:any;
declare var initNEWatchman:any;
declare var initWatchman:any;
declare var cefDebugMode:any;
declare var reactChannel:any;
declare var dbg:any;
declare var crashInfoObj:any;
declare var MENU_ITEMS_CACHE:any;
declare var h:any;
declare var f:any;
declare var __define__:any;
declare var _typeof:any;
declare var _defineProperty:any;
declare var ownKeys:any;
declare var _objectSpread2:any;
declare var MusicAPM:any;
declare var _MusicCorona:any;
declare var MusicCorona:any;
declare var corona:any;
declare var APP_CONF:any;
declare var NEJ:any;
declare var MWF:any;
declare var nej:any;
declare var mwf:any;
declare var nm:any;
declare var logger:any;
declare var localCache:any;
declare var id:any;
declare var windowMessage:any;
declare var TrimPath:any;
declare var oncallbackAfterConfigReady:any;
declare var lifecycle:any;
declare var jEmoji:any;
declare var pyOrder:any;
declare var ctl:any;
declare var onrequesterror:any;
declare var dc:any;
declare var app:any;
declare var QRCode:any;
declare var MIXIN_LIFECYCLE_PRE_INIT:any;
declare var frontendEncrypt:any;
declare var io:any;
declare var EventEmitter:any;
declare var pomelo:any;
declare var bootstrapApp:any;
declare var player:any;
declare var DesktopApmSdk:any;
declare var CMFrontEncryptedSDK:any;
declare var CMFrontEncryptedValidator:any;
declare var __mnb_globals__:any;
declare var MNB:any;
declare var api:any;
declare var $changeCall:any;
declare var $changeEnv:any;
declare var $getAppDownUrl:any;
declare var loadedPlugins:any;
declare var timeIndicator:any;
declare var _:any;
declare var onProcessLyrics:any;
declare var less:any;
declare var __EMOTION_REACT_11__:any;
declare var dispatcher:any;
declare var pinyin:any;
declare var JSEncrypt:any;
declare var NE_DAWN:any;
declare var Dawn:any;
declare var core:any;
declare var setImmediate:any;
declare var clearImmediate:any;
declare var WasmModule:any;
declare var __wmjsonp_7183de20:any;
declare var __wmjsonp_9ccbf511:any;
declare var __wmjsonp_e796d382:any;
declare var coverImageOnload:any;
declare var getCoverImage:any;
declare var getCoverStyle:any;
declare var Watchman:any;
declare var WM:any;
declare var currentLyrics:any;
declare var count:any;
declare var accentColorVariant:any;
declare var albumSize:any;