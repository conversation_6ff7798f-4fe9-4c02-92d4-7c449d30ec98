@circle-size: 20px;
@circle-border-width: 2px;
@circle-color-installed: #4caf50;
@circle-color-not-installed: #f44336;
@font-size-tag: 16px;
@font-size-details: 12px;
@font-color-tag: #fff;
@font-color-details: #9e9e9e;

.unm {
    padding: 10px;

    * {
        font-family: "Noto Sans", "Noto Sans SC", "Microsoft YaHei" !important;
    }

    .title {
        margin: 15px;
        font-size: 45px;
        font-weight: 800;
        color: #ffffff8c;

        .revived {
            font-size: 30px;
            font-weight: 600;
            color: rgb(255, 228, 196);
        }
    }

    .rr-btn {
        &_container {
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }

        &_button {
            transition: background 0.2s;
            border: none;
            border-radius: 5px;
            height: 40px;
            margin: 0 10px;
        }

        &_button:active {
            outline: none;
        }

        &_enabled {
            background-color: #3f7443;
        }

        &_disabled {
            background-color: #a84448;
        }
    }

    .optionBlock {
        padding: 20px;
        margin: 10px 10px;
        border-radius: 5px;
        background: rgb(19, 19, 19);

        .label {
            color: #ffffffd3;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .note {
            font-size: 13px;
            margin: 4px 0;
            color: #ffffff52;
        }

        .optionTitle {
            font-size: 27px;
            font-weight: 800;
            margin-bottom: 15px;
            color: #ffffff71;
        }

        .optionSubtitle {
            font-size: 17px;
            font-weight: 600;
        }

        &.versionSel {
            .version-selector {
                display: flex;
                flex-direction: column;
            }

            .version-item {
                display: flex;
                align-items: center;
                padding: 10px;
                cursor: pointer;
                margin: 5px;
                border-radius: 5px;
            }

            .circle {
                display: flex;
                align-items: center;
                justify-content: center;
                width: @circle-size;
                height: @circle-size;
                border-radius: 50%;
                border: @circle-border-width solid;
            }

            .circle-inner {
                width: @circle-size / 2;
                height: @circle-size / 2;
                border-radius: 50%;
                background-color: @circle-color-installed;
            }

            .circle.installed .circle-inner {
                background-color: @circle-color-installed;
            }

            .circle:not(.installed) .circle-inner {
                background-color: @circle-color-not-installed;
            }

            .info {
                margin-left: 10px;
            }

            .tag {
                font-size: @font-size-tag;
                font-weight: bold;
                color: @font-color-tag;
            }

            .details {
                font-size: @font-size-details;
                color: @font-color-details;
                margin-top: 5px;
            }

            .filename {
                margin-right: 10px;
            }

            .selected .circle {
                border-color: @circle-color-installed;
            }

            .selected:not(.installed) .circle {
                border-color: @circle-color-not-installed;
            }
        }

        .input-container {
            display: flex;
            flex-direction: column;
            margin-top: 10px;
            margin-bottom: 5px;
        }

        .input-label {
            color: #ffffffd3;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .input-box {
            height: 32px;
            border: none;
            border-radius: 4px;
            background-color: #2c2c2c;
            color: #ffffff;
            font-size: 14px;
            font-weight: 400;
            padding: 6px 12px;
            box-shadow: none;
            transition: background-color 0.2s ease, box-shadow 0.2s ease;

            &:hover {
                background-color: #3c3c3c;
            }

            &:focus {
                outline: none;
                background-color: #3c3c3c;
                box-shadow: 0 0 0 2px #5a5a5a;
            }

            &::placeholder {
                color: #9b9b9b;
            }

            margin-bottom: 3px;
        }


    }

    .btn {
        display: inline-block;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 1.5px;
        color: #ffffff;
        background-color: #222222;
        border: none;
        border-radius: 5px;
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.25);
        transition: all 0.3s ease-in-out;
    }

    .btn:hover {
        cursor: pointer;
        background-color: #333333;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.25);
    }

    .btn:active {
        background-color: #444444;
        box-shadow: none;
    }

    .btn:focus {
        outline: none;
        box-shadow: 0 0 3px 2px rgba(0, 0, 0, 0.2);
    }

    .badge-mixin();

    .badge-mixin() {
        .badge-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;

            &.badge-list-swap {
                .badge {
                    padding: 8px 0px;
                }
            }

            .badge-wrapper {
                cursor: grab;
            }

            .badge-wrapper:active {
                cursor: grabbing;
            }
        }

        .badge {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            padding: 8px 24px;
            border-radius: 700px;
            transition: box-shadow 0.2s, background-color 0.2s, color 0.2s;

            &-enabled {
                box-shadow: 0 0 0 2px #4bad4f, 3px 2px 6px #0000002f;
                background: #2c2c2c;
                color: white;
            }

            &-disabled {
                box-shadow: 0 0 0 0px #4bad4f;
                background-color: #222222;
                color: rgba(255, 255, 255, 0.527);
            }

            &-swap-button {
                border-radius: 100%;
                border: none;
                width: 18px;
                height: 18px;
                font-size: 13px;
                background: transparent;
                font-weight: 900;
                transition: background-color 0.2s, opacity 0.2s;
                opacity: 0;
                margin: 0 3px;

                &:hover {
                    cursor: pointer;
                    background-color: #3c3c3c;
                }
            }

            &:hover {
                cursor: pointer;
                background-color: #333333;

                .badge-swap-button {
                    opacity: 1;
                }
            }

            
        }

        .badge-text {
            font-size: 16px;
        }
    }

    .updown-mixin();

    .updown-mixin() {
        .updown-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            flex-direction: column;
        }

        .updown {
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 24px;
            border-radius: 10px;
            transition: box-shadow 0.2s, background-color 0.2s, color 0.2s;

            background: #2c2c2c;
            color: white;

            width: 300px;

            &:hover {
                box-shadow: 0 0 0 2px #4bad4f, 3px 2px 6px #0000002f;
                background-color: #333333;
            }
        }

        .updown-btns {
            display: flex;
            align-items: center;
        }

        .updown-btn {
            font-size: 16px;
            margin: 0 10px;

            &:hover {
                cursor: pointer;
            }
        }
    }
}
