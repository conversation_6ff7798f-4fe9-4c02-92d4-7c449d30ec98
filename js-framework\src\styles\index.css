@import url(./startup-warning.css);
@import url(./spinner.css);

.bncm-mgr,
.bncm-mgr * {
    box-sizing: border-box;
}

section.better-ncm-manager {
    display: none !important;
    background: transparent !important;
    z-index: 70;
    left: 200px;
    top: 60px;
    bottom: 73px;
    right: 0;
    overflow: hidden overlay;
    position: absolute;
}

section.better-ncm-manager.ncmm-show {
    display: block !important;
}

body.mq-playing section.better-ncm-manager.ncmm-show {
    display: none !important;
}

.bncm-mgr {
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 100%;
}

.bncm-mgr>* {
    display: flex;
    position: absolute;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.v-scroll {
    overflow: hidden;
    width: 100%;
    height: 100%;
}

.v-scroll>* {
    position: relative;
    overflow: hidden scroll;
    width: 100%;
    height: 100%;
}

.v-scroll>*>* {
    position: absolute;
    overflow: hidden scroll;
    width: 100%;
    height: 100%;
}

.safe-mode-info {
    overflow: hidden scroll;
    padding: 30px;
}

.safe-mode-info>* {
    margin: 1rem 0;
    font-size: small;
}

.safe-mode-info>h1 {
    font-size: medium;
    font-weight: bold;
}

.safe-mode-info>code {
    font-family: 'Courier New', Courier, monospace;
    user-select: text;
    cursor: text;
}

.bncm-mgr-header {
    padding: 30px !important;
    display: flex;
    flex-direction: row;
    justify-content: stretch;
    margin-bottom: 0;
}

.bncm-mgr-header>*:nth-child(2) {
    flex: 1;
    display: flex;
    padding-left: 30px;
    flex-direction: column;
    justify-content: space-around;
    align-items: stretch;
}

.loaded-plugins-list {
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 8px;
    width: 256px;
}

.plugin-btn-disabled {
    border-radius: 8px;
    padding: 8px;
    text-overflow: ellipsis;
    margin-bottom: 8px;
    opacity: 0.7;
}

.plugin-list-name{
    float: left;
    width: 85%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.plugin-btn {
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 8px;
}

.plugin-btn::after {
    display: table;
    clear: both;
    content: "";
}

.plugin-uninstall-btn{
    float: right;
    opacity: 0;
    transition: opacity 0.2s;
    width: 13px;
    cursor: pointer;
}

.plugin-uninstall-btn:hover{
    opacity: 1 !important;
}

.plugin-btn:hover .plugin-uninstall-btn{
    opacity: 0.4;
}

.plugin-btn:not(.plugin-btn-disabled):hover,
.plugin-btn.selected {
    background-color: rgba(136, 136, 136, 0.116);
}

.bncm-mgr-header>*:nth-child(2)>h1 {
    font-size: large;
    font-weight: bold;
}

.bncm-mgr-btns {
    display: flex;
}

.bncm-mgr-btns>* {
    margin-right: 10px;
}

.header-ctl {
    margin: 28px 0 0 30px;
}

@keyframes twinkling {
    0%{
        opacity: 1;
    }
    50%{
        opacity: 0.6;
    }
    100%{
        opacity: 1;
    }
}

.bncm-btn-twinkling{
    animation: twinkling 1s infinite;
}