# RevivedUnblockInstaller
## BetterNCM 插件，一键安装解灰

![image](https://github.com/ReviveUnblockNCMInstaller/RevivedUnblockInstaller/assets/129636358/aa140574-c3a9-4404-ab7c-582a825e5062)

![preview2](https://user-images.githubusercontent.com/129636358/230762468-fa79e566-2f32-42be-9f95-f54a0e9beffd.png)

![preview3](https://user-images.githubusercontent.com/129636358/230757757-21f8d7a3-39fc-4477-b6c6-6ecaa86b3756.png)




### 如何安装？

1. 安装 [BetterNCM](https://github.com/MicroCBer/BetterNCM) 插件管理器
2. 点击 右上角出现的云音乐按钮
3. 安装 RevivedSource
4. 重载后，安装 RevivedUnblockInstaller

### 如何使用？

1. 进入 插件设置（点击 RevivedUnblockInstaller）
2. 点击 “已禁用” 按钮来启用
3. 选择你需要的版本（国内用户推荐 `-ghproxy` 的版本），点击应用
4. 按流程操作即可
