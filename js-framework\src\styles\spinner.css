.bncm-spinner {
    display: inline-block;
    position: relative;
}

.bncm-spinner div {
    width: 100%;
    height: 100%;
    position: absolute;
    transform-origin: 50% 50%;
    animation: lds-spinner 1.2s linear infinite;
}

.bncm-spinner div:after {
    content: " ";
    display: block;
    position: relative;
    top: 3.75%;
    left: 46.25%;
    width: 7.5%;
    height: 22.5%;
    border-radius: 20%;
    background: #fff;
}

.bncm-spinner div:nth-child(1) {
    transform: rotate(0deg);
    animation-delay: -1.1s;
}

.bncm-spinner div:nth-child(2) {
    transform: rotate(30deg);
    animation-delay: -1s;
}

.bncm-spinner div:nth-child(3) {
    transform: rotate(60deg);
    animation-delay: -0.9s;
}

.bncm-spinner div:nth-child(4) {
    transform: rotate(90deg);
    animation-delay: -0.8s;
}

.bncm-spinner div:nth-child(5) {
    transform: rotate(120deg);
    animation-delay: -0.7s;
}

.bncm-spinner div:nth-child(6) {
    transform: rotate(150deg);
    animation-delay: -0.6s;
}

.bncm-spinner div:nth-child(7) {
    transform: rotate(180deg);
    animation-delay: -0.5s;
}

.bncm-spinner div:nth-child(8) {
    transform: rotate(210deg);
    animation-delay: -0.4s;
}

.bncm-spinner div:nth-child(9) {
    transform: rotate(240deg);
    animation-delay: -0.3s;
}

.bncm-spinner div:nth-child(10) {
    transform: rotate(270deg);
    animation-delay: -0.2s;
}

.bncm-spinner div:nth-child(11) {
    transform: rotate(300deg);
    animation-delay: -0.1s;
}

.bncm-spinner div:nth-child(12) {
    transform: rotate(330deg);
    animation-delay: 0s;
}

@keyframes lds-spinner {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}